/* Blueprint Theme System */
@import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');

/* CSS Variables - Blueprint Theme System */
:root {
  /* Dark Theme Colors (Default) */
  --color-dark-bg: #0a0a0a;
  --color-dark-card: #111111;
  --color-dark-border: #222222;
  --color-dark-text: #ffffff;
  --color-dark-text-secondary: #a1a1aa;

  /* Light Theme Colors */
  --color-light-bg: #f1f3f4;
  --color-light-card: #fefefe;
  --color-light-border: #e5e7eb;
  --color-light-text: #1f2937;
  --color-light-text-secondary: #6b7280;

  /* Neon/Cyberpunk Colors - Dark Mode */
  --color-neon-green: #00ff88;
  --color-neon-blue: #00d4ff;
  --color-cyber-purple: #8b5cf6;

  /* Light Mode Accent Colors */
  --color-light-accent-blue: #2563eb;
  --color-light-accent-green: #059669;
  --color-light-accent-purple: #7c3aed;

  /* Adaptive Colors (Change based on theme) */
  --color-bg: var(--color-dark-bg);
  --color-card: var(--color-dark-card);
  --color-border: var(--color-dark-border);
  --color-text: var(--color-dark-text);
  --color-text-secondary: var(--color-dark-text-secondary);
  --color-accent-blue: var(--color-neon-blue);
  --color-accent-green: var(--color-neon-green);
  --color-accent-purple: var(--color-cyber-purple);
}

/* Light Theme Override */
[data-theme='light'] {
  --color-bg: var(--color-light-bg);
  --color-card: var(--color-light-card);
  --color-border: var(--color-light-border);
  --color-text: var(--color-light-text);
  --color-text-secondary: var(--color-light-text-secondary);
  --color-accent-blue: var(--color-light-accent-blue);
  --color-accent-green: var(--color-light-accent-green);
  --color-accent-purple: var(--color-light-accent-purple);
}

/* Base Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Body with Blueprint styling */
body {
  font-family: 'Space Grotesk', 'Inter', system-ui, sans-serif;
  background-color: var(--color-bg);
  color: var(--color-text);
  line-height: 1.6;
  overflow-x: hidden;
  min-height: 100vh;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Dark mode background pattern */
[data-theme='dark'] body {
  background-image: radial-gradient(
    circle at 1px 1px,
    rgba(0, 245, 255, 0.15) 1px,
    transparent 0
  );
  background-size: 20px 20px;
}

/* Light mode background pattern */
[data-theme='light'] body {
  background-image: radial-gradient(
    circle at 1px 1px,
    rgba(37, 99, 235, 0.08) 1px,
    transparent 0
  );
  background-size: 20px 20px;
}

/* Navbar - Blueprint Style */
.navbar {
  position: fixed;
  top: 0;
  z-index: 50;
  width: 100%;
  background-color: rgba(17, 17, 17, 0.9);
  backdrop-filter: blur(12px);
  border-bottom: 1px solid var(--color-border);
  transition: all 0.3s ease;
}

[data-theme='light'] .navbar {
  background-color: rgba(254, 254, 254, 0.9);
}

.navbar-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 4rem;
}

.navbar-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--color-accent-blue);
  text-decoration: none;
  transition: all 0.3s ease;
}

.navbar-logo:hover {
  transform: scale(1.05);
}

[data-theme='dark'] .navbar-logo {
  text-shadow: 0 0 10px currentColor;
}

.theme-toggle {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
  padding: 0.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-toggle:hover {
  background-color: rgba(0, 212, 255, 0.1);
  border-color: var(--color-accent-blue);
  transform: translateY(-1px);
}

[data-theme='dark'] .theme-toggle:hover {
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
}

/* Main Content */
.main-content {
  flex: 1;
  padding-top: 4rem;
}

/* Container */
.container {
  max-width: 1152px;
  margin: 0 auto;
  padding: 1rem;
}

/* Hero Section - Blueprint Style */
.hero {
  height: 15vh;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
}

.hero h1 {
  font-size: clamp(2rem, 5vw, 4rem);
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(
    135deg,
    var(--color-accent-blue),
    var(--color-accent-purple)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

[data-theme='dark'] .hero h1 {
  text-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}

.hero p {
  font-size: 1.125rem;
  color: var(--color-text-secondary);
  max-width: 48rem;
  margin: 0 auto;
}

/* Cards - Blueprint Style */
.card {
  background-color: var(--color-card);
  border: 1px solid var(--color-border);
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
}

[data-theme='dark'] .card:hover {
  box-shadow: 0 10px 25px rgba(0, 212, 255, 0.1),
    0 0 0 1px rgba(0, 212, 255, 0.2);
}

[data-theme='light'] .card:hover {
  box-shadow: 0 10px 25px rgba(37, 99, 235, 0.1),
    0 0 0 1px rgba(37, 99, 235, 0.2);
}

.section-header {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--color-accent-blue);
  margin-bottom: 1rem;
  position: relative;
  padding-bottom: 0.5rem;
}

.section-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background: linear-gradient(
    90deg,
    var(--color-accent-blue),
    var(--color-accent-purple)
  );
  border-radius: 2px;
}

[data-theme='dark'] .section-header::after {
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.demo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.demo-box {
  background-color: var(--color-card);
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.demo-box:hover {
  transform: translateY(-4px);
}

[data-theme='dark'] .demo-box:hover {
  box-shadow: 0 10px 25px rgba(0, 212, 255, 0.2),
    0 0 0 1px rgba(0, 212, 255, 0.3);
  background: linear-gradient(
    135deg,
    rgba(0, 212, 255, 0.05),
    rgba(139, 92, 246, 0.05)
  );
}

.animated-box {
  width: 80px;
  height: 80px;
  background: linear-gradient(
    135deg,
    var(--color-accent-blue),
    var(--color-accent-purple)
  );
  border-radius: 0.5rem;
  margin: 0 auto;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  transition: all 0.3s ease;
}

[data-theme='dark'] .animated-box {
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}

.btn-primary {
  background: linear-gradient(
    135deg,
    var(--color-accent-blue),
    var(--color-accent-purple)
  );
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  position: relative;
  overflow: hidden;
}

.btn-primary:hover {
  transform: translateY(-2px);
}

[data-theme='dark'] .btn-primary:hover {
  box-shadow: 0 10px 25px rgba(0, 212, 255, 0.4);
}

.btn-secondary {
  background: transparent;
  color: var(--color-accent-blue);
  border: 2px solid var(--color-accent-blue);
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.btn-secondary:hover {
  background-color: var(--color-accent-blue);
  color: white;
  transform: translateY(-2px);
}

.code-viewer {
  background-color: var(--color-card);
  border: 1px solid var(--color-border);
  border-radius: 0.75rem;
  margin-bottom: 1.5rem;
  overflow: hidden;
}

.code-header {
  background-color: rgba(0, 0, 0, 0.2);
  padding: 0.75rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--color-border);
}

.code-title {
  font-family: 'JetBrains Mono', monospace;
  font-size: 0.875rem;
  color: var(--color-text-secondary);
  font-weight: 500;
}

.copy-button {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  cursor: pointer;
  font-size: 0.75rem;
  transition: all 0.3s ease;
  font-family: 'JetBrains Mono', monospace;
}

.copy-button:hover {
  color: var(--color-accent-blue);
  border-color: var(--color-accent-blue);
  background-color: rgba(0, 212, 255, 0.1);
}

.code-content {
  padding: 1rem;
  font-family: 'JetBrains Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.6;
  color: var(--color-text);
  background-color: rgba(0, 0, 0, 0.1);
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 300px;
  overflow-y: auto;
}

[data-theme='light'] .code-viewer {
  background-color: rgba(249, 250, 251, 0.8);
}

[data-theme='light'] .code-header {
  background-color: rgba(107, 114, 128, 0.1);
}

[data-theme='light'] .code-title,
[data-theme='light'] .code-content {
  color: var(--color-light-text);
}

[data-theme='light'] .copy-button {
  color: var(--color-light-text-secondary);
}

[data-theme='light'] .copy-button:hover {
  color: var(--color-light-accent-blue);
  border-color: var(--color-light-accent-blue);
}

.show-code-btn {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  margin: 1rem 0;
  font-family: 'JetBrains Mono', monospace;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.show-code-btn:hover {
  color: var(--color-accent-blue);
  border-color: var(--color-accent-blue);
  background-color: rgba(0, 212, 255, 0.05);
}

.list-item {
  background-color: var(--color-card);
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.list-item:hover {
  transform: translateX(5px);
  border-color: var(--color-accent-blue);
}

.progress-container {
  background-color: var(--color-card);
  border: 1px solid var(--color-border);
  border-radius: 1rem;
  padding: 1rem;
  height: 20px;
  position: relative;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(
    90deg,
    var(--color-accent-blue),
    var(--color-accent-purple)
  );
  border-radius: 0.5rem;
  transition: width 0.3s ease;
}

[data-theme='dark'] .progress-bar {
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.5);
}

/* Modal Styles */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  z-index: 1000;
}

.modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: var(--color-card);
  border: 1px solid var(--color-border);
  border-radius: 1rem;
  padding: 2rem;
  z-index: 1001;
  max-width: 450px;
  width: 90%;
}

[data-theme='dark'] .modal {
  box-shadow: 0 25px 50px rgba(0, 212, 255, 0.2);
}

/* Chart Components */
.chart-container {
  display: flex;
  align-items: end;
  justify-content: space-around;
  height: 200px;
  background-color: var(--color-card);
  border: 1px solid var(--color-border);
  border-radius: 1rem;
  padding: 1.5rem;
}

.bar {
  width: 40px;
  background: linear-gradient(
    to top,
    var(--color-accent-blue),
    var(--color-accent-purple)
  );
  border-radius: 0.5rem 0.5rem 0 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.bar:hover {
  transform: scale(1.05);
}

[data-theme='dark'] .bar {
  box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
}

/* Skeleton Loading */
.skeleton-card {
  background-color: var(--color-card);
  border: 1px solid var(--color-border);
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin: 1rem;
}

.skeleton-line {
  height: 1.5rem;
  background-color: var(--color-border);
  border-radius: 0.375rem;
  margin: 0.75rem 0;
}

.skeleton-line.short {
  width: 65%;
}

/* Drag Card */
.drag-card {
  background-color: var(--color-card);
  border: 1px solid var(--color-border);
  border-radius: 1rem;
  padding: 1.5rem;
  margin: 1rem;
  cursor: grab;
  user-select: none;
  transition: all 0.3s ease;
}

.drag-card:active {
  cursor: grabbing;
}

.drag-card:hover {
  border-color: var(--color-accent-blue);
}

.demo-description {
  color: var(--color-text-secondary);
  font-size: 0.875rem;
  margin-bottom: 1rem;
  line-height: 1.6;
  text-align: center;
}

.final-section {
  background: linear-gradient(
    135deg,
    rgba(0, 212, 255, 0.05),
    rgba(139, 92, 246, 0.05)
  );
  border: 1px solid var(--color-border);
  border-radius: 1rem;
  padding: 3rem 2rem;
  text-align: center;
  margin-top: 4rem;
  position: relative;
  overflow: hidden;
}

.final-section h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: var(--color-accent-blue);
}

.final-section h2::after {
  content: '✨';
  margin-left: 0.5rem;
}

.final-section p {
  font-size: 1.125rem;
  color: var(--color-text-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .navbar-container {
    padding: 0 0.5rem;
  }

  .hero h1 {
    font-size: 2.5rem;
  }

  .demo-grid {
    grid-template-columns: 1fr;
  }

  .container {
    padding: 0.5rem;
  }

  .card {
    padding: 1rem;
  }

  .demo-box {
    min-height: 120px;
    padding: 1.5rem;
  }
}

/* Animations */
@keyframes glow {
  0%,
  100% {
    box-shadow: 0 0 5px currentColor;
  }
  50% {
    box-shadow: 0 0 20px currentColor, 0 0 30px currentColor;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

/* Utility Classes */
.text-gradient {
  background: linear-gradient(
    135deg,
    var(--color-accent-blue),
    var(--color-accent-purple)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glow-text {
  text-shadow: 0 0 10px currentColor;
}
