import Navbar from './components/Navbar';
import AccessibilityAnimations from './components/examples/AccessibilityAnimations';
import BasicAnimations from './components/examples/BasicAnimations';
import ComplexSequences from './components/examples/ComplexSequences';
import CustomHooks from './components/examples/CustomHooks';
import DataVisualization from './components/examples/DataVisualization';
import GestureInteractions from './components/examples/GestureInteractions';
import LayoutAnimations from './components/examples/LayoutAnimations';
import LoadingStates from './components/examples/LoadingStates';
import ModalsOverlays from './components/examples/ModalsOverlays';
import PageTransitions from './components/examples/PageTransitions';
import PerformanceOptimizations from './components/examples/PerformanceOptimizations';
import PhysicsAnimations from './components/examples/PhysicsAnimations';
import ScrollAnimations from './components/examples/ScrollAnimations';

function App() {
  return (
    <div className="app">
      <Navbar />

      <main className="main-content">
        <div className="container">
          {/* Hero Section */}
          <section className="hero">
            <h1>Framer Motion Examples</h1>
            <p>
              Explore advanced animation techniques with Framer Motion and
              React. Interactive examples showcasing the power of modern web
              animations.
            </p>
          </section>

          {/* Examples */}
          <BasicAnimations />
          <ComplexSequences />
          <PhysicsAnimations />
          <GestureInteractions />
          <LayoutAnimations />
          <ScrollAnimations />
          <PerformanceOptimizations />
          <CustomHooks />
          <PageTransitions />
          <AccessibilityAnimations />
          <LoadingStates />
          <DataVisualization />
          <ModalsOverlays />

          {/* Final Section */}
          <section className="final-section">
            <h2>Ready to Animate?</h2>
            <p>
              These examples showcase the incredible power of Framer Motion.
              Start building your own animations today and bring your interfaces
              to life!
            </p>
          </section>
        </div>
      </main>
    </div>
  );
}

export default App;
