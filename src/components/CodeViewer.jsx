import { Check, Copy } from 'lucide-react';
import { useState } from 'react';

const CodeViewer = ({ code, title = 'Code' }) => {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy code: ', err);
    }
  };

  return (
    <div className="code-viewer">
      <div className="code-header">
        <span className="code-title">{title}</span>
        <button onClick={copyToClipboard} className="copy-button">
          {copied ? <Check size={14} /> : <Copy size={14} />}
          {copied ? 'Copied!' : 'Copy'}
        </button>
      </div>
      <div className="code-content">
        <pre>{code}</pre>
      </div>
    </div>
  );
};

export default CodeViewer;
