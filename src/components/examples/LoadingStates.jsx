import { motion } from 'framer-motion';
import { Loader2, Play } from 'lucide-react';
import { useState } from 'react';
import <PERSON><PERSON>ie<PERSON> from '../CodeViewer';
import DemoBox from '../DemoBox';

const LoadingStates = () => {
  const [showCode, setShowCode] = useState(false);

  const codeExample = `import { motion } from 'framer-motion';

// Loading Skeleton
function LoadingSkeleton() {
  return (
    <motion.div
      className="skeleton-card"
      animate={{
        backgroundColor: [
          'var(--color-border)',
          'rgba(0, 212, 255, 0.1)',
          'var(--color-border)',
        ],
      }}
      transition={{
        duration: 1.5,
        repeat: Infinity,
        ease: 'easeInOut',
      }}
    >
      <div className="skeleton-line" />
      <div className="skeleton-line short" />
      <div className="skeleton-line" />
    </motion.div>
  );
}

// Progress Bar
function ProgressBar() {
  const [progress, setProgress] = useState(0);

  const startProgress = () => {
    setProgress(0);
    const interval = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval);
          return 100;
        }
        return prev + 2;
      });
    }, 50);
  };

  return (
    <div>
      <button onClick={startProgress}>Start Progress</button>
      <div className="progress-container">
        <motion.div
          className="progress-bar"
          initial={{ width: '0%' }}
          animate={{ width: \`\${progress}%\` }}
          transition={{ duration: 0.1 }}
        />
      </div>
      <p>{progress}% complete</p>
    </div>
  );
}`;

  // Loading Skeleton
  function LoadingSkeleton() {
    return (
      <motion.div
        className="skeleton-card"
        animate={{
          backgroundColor: [
            'var(--color-border)',
            'rgba(0, 212, 255, 0.1)',
            'var(--color-border)',
          ],
        }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          ease: 'easeInOut',
        }}>
        <div className="skeleton-line" />
        <div className="skeleton-line short" />
        <div className="skeleton-line" />
      </motion.div>
    );
  }

  // Progress Bar
  function ProgressBar() {
    const [progress, setProgress] = useState(0);

    const startProgress = () => {
      setProgress(0);
      const interval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 100) {
            clearInterval(interval);
            return 100;
          }
          return prev + 2;
        });
      }, 50);
    };

    return (
      <div>
        <button
          className="btn-secondary"
          onClick={startProgress}
          style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
          <Play size={16} />
          Start Progress
        </button>
        <div className="progress-container">
          <motion.div
            className="progress-bar"
            initial={{ width: '0%' }}
            animate={{ width: `${progress}%` }}
            transition={{ duration: 0.1 }}
          />
        </div>
        <p className="demo-description">{progress}% complete</p>
      </div>
    );
  }

  // Spinner Animation with Lucide Icon
  function SpinnerAnimation() {
    return (
      <motion.div
        animate={{ rotate: 360 }}
        transition={{
          duration: 1,
          repeat: Infinity,
          ease: 'linear',
        }}
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          color: 'var(--color-accent-blue)',
        }}>
        <Loader2 size={40} />
      </motion.div>
    );
  }

  return (
    <div className="card">
      <h2 className="section-header">11. Loading States</h2>
      <p className="demo-description">
        Skeleton screens and progress indicators with smooth, continuous
        animations.
      </p>

      <div className="demo-grid">
        <DemoBox>
          <LoadingSkeleton />
          <p className="demo-description">Skeleton loading animation</p>
        </DemoBox>

        <DemoBox>
          <ProgressBar />
        </DemoBox>

        <DemoBox>
          <SpinnerAnimation />
          <p className="demo-description">Loading spinner</p>
        </DemoBox>
      </div>

      <button onClick={() => setShowCode(!showCode)} className="show-code-btn">
        {showCode ? 'Hide Code' : 'Show Code'}
      </button>

      {showCode && (
        <CodeViewer
          code={codeExample}
          title="Loading States - React/Framer Motion"
        />
      )}
    </div>
  );
};

export default LoadingStates;
