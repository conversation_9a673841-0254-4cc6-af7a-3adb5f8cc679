import { motion } from 'framer-motion';
import { useState } from 'react';
import <PERSON><PERSON><PERSON><PERSON> from '../CodeViewer';
import De<PERSON>Box from '../DemoBox';

const LoadingStates = () => {
  const [showCode, setShowCode] = useState(false);

  const codeExample = `import { motion } from 'framer-motion';

// Loading Skeleton
function LoadingSkeleton() {
  return (
    <motion.div
      className="skeleton-card"
      animate={{
        backgroundColor: [
          'var(--color-border)',
          'rgba(0, 212, 255, 0.1)',
          'var(--color-border)',
        ],
      }}
      transition={{
        duration: 1.5,
        repeat: Infinity,
        ease: 'easeInOut',
      }}
    >
      <div className="skeleton-line" />
      <div className="skeleton-line short" />
      <div className="skeleton-line" />
    </motion.div>
  );
}

// Progress Bar
function ProgressBar() {
  const [progress, setProgress] = useState(0);

  const startProgress = () => {
    setProgress(0);
    const interval = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval);
          return 100;
        }
        return prev + 2;
      });
    }, 50);
  };

  return (
    <div>
      <button onClick={startProgress}>Start Progress</button>
      <div className="progress-container">
        <motion.div
          className="progress-bar"
          initial={{ width: '0%' }}
          animate={{ width: \`\${progress}%\` }}
          transition={{ duration: 0.1 }}
        />
      </div>
      <p>{progress}% complete</p>
    </div>
  );
}`;

  // Loading Skeleton
  function LoadingSkeleton() {
    return (
      <motion.div
        className="skeleton-card"
        animate={{
          backgroundColor: [
            'var(--color-border)',
            'rgba(0, 212, 255, 0.1)',
            'var(--color-border)',
          ],
        }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          ease: 'easeInOut',
        }}>
        <div className="skeleton-line" />
        <div className="skeleton-line short" />
        <div className="skeleton-line" />
      </motion.div>
    );
  }

  // Progress Bar
  function ProgressBar() {
    const [progress, setProgress] = useState(0);

    const startProgress = () => {
      setProgress(0);
      const interval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 100) {
            clearInterval(interval);
            return 100;
          }
          return prev + 2;
        });
      }, 50);
    };

    return (
      <div>
        <button className="btn-secondary" onClick={startProgress}>
          Start Progress
        </button>
        <div className="progress-container">
          <motion.div
            className="progress-bar"
            initial={{ width: '0%' }}
            animate={{ width: `${progress}%` }}
            transition={{ duration: 0.1 }}
          />
        </div>
        <p className="demo-description">{progress}% complete</p>
      </div>
    );
  }

  // Spinner Animation
  function SpinnerAnimation() {
    return (
      <motion.div
        animate={{ rotate: 360 }}
        transition={{
          duration: 1,
          repeat: Infinity,
          ease: "linear"
        }}
        style={{
          width: '40px',
          height: '40px',
          border: '4px solid var(--color-border)',
          borderTop: '4px solid var(--color-accent-blue)',
          borderRadius: '50%',
          margin: '0 auto'
        }}
      />
    );
  }

  return (
    <div className="card">
      <h2 className="section-header">11. Loading States</h2>
      <p className="demo-description">
        Skeleton screens and progress indicators with smooth, continuous animations.
      </p>

      <div className="demo-grid">
        <DemoBox>
          <LoadingSkeleton />
          <p className="demo-description">Skeleton loading animation</p>
        </DemoBox>

        <DemoBox>
          <ProgressBar />
        </DemoBox>

        <DemoBox>
          <SpinnerAnimation />
          <p className="demo-description">Loading spinner</p>
        </DemoBox>
      </div>

      <button onClick={() => setShowCode(!showCode)} className="show-code-btn">
        {showCode ? 'Hide Code' : 'Show Code'}
      </button>

      {showCode && (
        <CodeViewer
          code={codeExample}
          title="Loading States - React/Framer Motion"
        />
      )}
    </div>
  );
};

export default LoadingStates;
