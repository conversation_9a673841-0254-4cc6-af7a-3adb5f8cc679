import { motion, useScroll, useTransform } from 'framer-motion';
import { useEffect, useRef, useState } from 'react';
import <PERSON>Viewer from '../CodeViewer';
import DemoBox from '../DemoBox';

const ScrollAnimations = () => {
  const [showCode, setShowCode] = useState(false);
  const containerRef = useRef(null);
  const { scrollYProgress } = useScroll({ target: containerRef });

  const scale = useTransform(scrollYProgress, [0, 1], [0.8, 1.2]);
  const rotate = useTransform(scrollYProgress, [0, 1], [0, 360]);
  const opacity = useTransform(scrollYProgress, [0, 0.5, 1], [0.5, 1, 0.5]);

  // Auto scroll demo
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const autoScroll = () => {
      const maxScroll = container.scrollHeight - container.clientHeight;
      let currentScroll = 0;
      const scrollStep = maxScroll / 100;

      const interval = setInterval(() => {
        currentScroll += scrollStep;
        if (currentScroll >= maxScroll) {
          currentScroll = 0;
        }
        container.scrollTop = currentScroll;
      }, 50);

      return interval;
    };

    const interval = autoScroll();
    return () => clearInterval(interval);
  }, []);

  const codeExample = `import { motion, useScroll, useTransform } from 'framer-motion';

const { scrollYProgress } = useScroll({ target: containerRef });

const scale = useTransform(scrollYProgress, [0, 1], [0.8, 1.2]);
const rotate = useTransform(scrollYProgress, [0, 1], [0, 360]);
const opacity = useTransform(scrollYProgress, [0, 0.5, 1], [0.5, 1, 0.5]);

<motion.div
  style={{ scale, rotate, opacity }}
  className="animated-box"
>
  Scroll Me!
</motion.div>`;

  return (
    <div className="card">
      <h2 className="section-header">7. Scroll Animations</h2>
      <p className="demo-description">
        Scroll-driven animations using useScroll and useTransform with motion
        values.
      </p>

      <div className="demo-grid">
        <DemoBox>
          <div
            ref={containerRef}
            style={{
              height: '150px',
              overflowY: 'scroll',
              border: '1px solid var(--color-border)',
              borderRadius: '8px',
              padding: '1rem',
            }}>
            <div style={{ height: '300px', position: 'relative' }}>
              <motion.div
                style={{ scale, rotate, opacity }}
                className="animated-box"
                css={{ position: 'sticky', top: '50px' }}>
                Scroll Me!
              </motion.div>
              <div
                style={{
                  marginTop: '2rem',
                  fontSize: '12px',
                  color: 'var(--color-text-secondary)',
                }}>
                <p>Keep scrolling to see the animation...</p>
                <p>The element transforms based on scroll progress.</p>
                <p>Scale, rotation, and opacity change dynamically.</p>
                <p>This creates engaging scroll-driven experiences.</p>
              </div>
            </div>
          </div>
        </DemoBox>

        <DemoBox>
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6 }}
            className="animated-box"
            style={{
              background: `linear-gradient(45deg, 
                hsl(${Math.random() * 360}, 70%, 60%), 
                hsl(${Math.random() * 360}, 70%, 60%))`,
            }}>
            Scroll Reveal
          </motion.div>
        </DemoBox>

        <DemoBox>
          <div style={{ position: 'relative' }}>
            <motion.div
              className="progress-container"
              style={{ width: '100%', height: '8px', margin: '1rem 0' }}>
              <motion.div
                className="progress-bar"
                style={{
                  width: useTransform(scrollYProgress, [0, 1], ['0%', '100%']),
                  height: '100%',
                }}
              />
            </motion.div>
            <p
              style={{
                fontSize: '12px',
                color: 'var(--color-text-secondary)',
                textAlign: 'center',
              }}>
              Scroll Progress Indicator
            </p>
          </div>
        </DemoBox>
      </div>

      <button onClick={() => setShowCode(!showCode)} className="show-code-btn">
        {showCode ? 'Hide Code' : 'Show Code'}
      </button>

      {showCode && (
        <CodeViewer
          code={codeExample}
          title="Scroll Animations - React/Framer Motion"
        />
      )}
    </div>
  );
};

export default ScrollAnimations;
