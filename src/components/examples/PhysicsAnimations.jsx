import { motion } from 'framer-motion';
import { useState } from 'react';
import <PERSON><PERSON><PERSON><PERSON> from '../CodeViewer';
import De<PERSON>B<PERSON> from '../DemoBox';

const PhysicsAnimations = () => {
  const [showCode, setShowCode] = useState(false);

  const codeExample = `import { motion } from 'framer-motion';

// Spring Animation
function SpringAnimation() {
  const [clicked, setClicked] = useState(false);

  return (
    <motion.div
      className="animated-box"
      animate={{
        scale: clicked ? 1.2 : 1,
        rotate: clicked ? 180 : 0,
      }}
      transition={{
        type: 'spring',
        stiffness: 260,
        damping: 20,
        mass: 1,
      }}
      onClick={() => setClicked(!clicked)}
      style={{ cursor: 'pointer' }}
    >
      🎯
    </motion.div>
  );
}

// Advanced Spring Physics
<motion.div
  animate={{ x: [0, 50, 0] }}
  transition={{
    type: "spring",
    stiffness: 300,
    damping: 30,
    repeat: Infinity
  }}
>
  Spring Motion
</motion.div>`;

  // Spring Animation
  function SpringAnimation() {
    const [clicked, setClicked] = useState(false);

    return (
      <motion.div
        className="animated-box"
        animate={{
          scale: clicked ? 1.2 : 1,
          rotate: clicked ? 180 : 0,
        }}
        transition={{
          type: 'spring',
          stiffness: 260,
          damping: 20,
          mass: 1,
        }}
        onClick={() => setClicked(!clicked)}
        style={{ cursor: 'pointer' }}>
        🎯
      </motion.div>
    );
  }

  return (
    <div className="card">
      <h2 className="section-header">3. Physics-Based Animations</h2>
      <p className="demo-description">
        Using spring system with mass, stiffness, and damping for natural, organic movement.
      </p>

      <div className="demo-grid">
        <DemoBox>
          <SpringAnimation />
          <p className="demo-description">Click to interact with spring physics</p>
        </DemoBox>

        <DemoBox>
          <motion.div
            animate={{ x: [0, 50, 0] }}
            transition={{
              type: 'spring',
              stiffness: 300,
              damping: 30,
              repeat: Infinity,
              repeatDelay: 1,
            }}
            className="animated-box">
            Spring
          </motion.div>
          <p className="demo-description">Continuous spring motion</p>
        </DemoBox>

        <DemoBox>
          <motion.div
            animate={{
              y: [0, -30, 0],
              scale: [1, 0.8, 1],
            }}
            transition={{
              type: 'spring',
              stiffness: 400,
              damping: 10,
              repeat: Infinity,
              repeatDelay: 0.5,
            }}
            className="animated-box">
            Bounce
          </motion.div>
          <p className="demo-description">Bouncing with spring physics</p>
        </DemoBox>
      </div>

      <button onClick={() => setShowCode(!showCode)} className="show-code-btn">
        {showCode ? 'Hide Code' : 'Show Code'}
      </button>

      {showCode && (
        <CodeViewer
          code={codeExample}
          title="Physics Animations - React/Framer Motion"
        />
      )}
    </div>
  );
};

export default PhysicsAnimations;
