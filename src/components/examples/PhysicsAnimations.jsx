import { motion } from 'framer-motion';
import { useState } from 'react';
import <PERSON><PERSON><PERSON><PERSON> from '../CodeViewer';
import De<PERSON>Box from '../DemoBox';

const PhysicsAnimations = () => {
  const [showCode, setShowCode] = useState(false);
  const [triggerBounce, setTriggerBounce] = useState(0);

  const springConfig = {
    type: 'spring',
    stiffness: 300,
    damping: 20,
    mass: 1,
  };

  const codeExample = `import { motion } from 'framer-motion';

const springConfig = {
  type: "spring",
  stiffness: 300,
  damping: 20,
  mass: 1
};

// Bouncy Spring Animation
<motion.div
  animate={{ y: [0, -100, 0] }}
  transition={{
    ...springConfig,
    duration: 2,
    repeat: Infinity
  }}
  className="animated-box"
>
  Bouncing Ball
</motion.div>

// Elastic Scale
<motion.div
  whileHover={{ 
    scale: 1.2,
    transition: { type: "spring", stiffness: 400, damping: 10 }
  }}
  className="animated-box"
>
  Elastic
</motion.div>`;

  return (
    <div className="card">
      <h2 className="section-header">3. Physics-Based Animations</h2>
      <p className="demo-description">
        Natural physics simulations using spring configurations for realistic
        motion.
      </p>

      <div className="demo-grid">
        <DemoBox>
          <motion.div
            key={triggerBounce}
            animate={{ y: [0, -60, 0] }}
            transition={{
              ...springConfig,
              duration: 1.5,
              repeat: 2,
            }}
            className="animated-box"
            style={{ borderRadius: '50%' }}>
            ⚽
          </motion.div>
          <button
            onClick={() => setTriggerBounce((prev) => prev + 1)}
            className="btn-secondary"
            style={{ marginTop: '1rem', fontSize: '0.75rem' }}>
            Bounce Ball
          </button>
        </DemoBox>

        <DemoBox>
          <motion.div
            whileHover={{
              scale: 1.3,
              transition: { type: 'spring', stiffness: 400, damping: 10 },
            }}
            whileTap={{
              scale: 0.8,
              transition: { type: 'spring', stiffness: 600, damping: 15 },
            }}
            className="animated-box">
            Elastic
          </motion.div>
        </DemoBox>

        <DemoBox>
          <motion.div
            animate={{
              rotate: [0, 360],
              scale: [1, 1.2, 1],
            }}
            transition={{
              rotate: { duration: 2, repeat: Infinity, ease: 'linear' },
              scale: {
                type: 'spring',
                stiffness: 200,
                damping: 15,
                repeat: Infinity,
                repeatType: 'reverse',
              },
            }}
            className="animated-box"
            style={{
              background:
                'conic-gradient(from 0deg, #00d4ff, #8b5cf6, #00ff88, #00d4ff)',
            }}>
            🌟
          </motion.div>
        </DemoBox>
      </div>

      <button onClick={() => setShowCode(!showCode)} className="show-code-btn">
        {showCode ? 'Hide Code' : 'Show Code'}
      </button>

      {showCode && (
        <CodeViewer
          code={codeExample}
          title="Physics Animations - React/Framer Motion"
        />
      )}
    </div>
  );
};

export default PhysicsAnimations;
