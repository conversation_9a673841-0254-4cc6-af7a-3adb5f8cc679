import { motion } from 'framer-motion';
import { useState } from 'react';
import <PERSON><PERSON>ie<PERSON> from '../CodeViewer';
import DemoBox from '../DemoBox';

const GestureInteractions = () => {
  const [showCode, setShowCode] = useState(false);
  const [dragCount, setDragCount] = useState(0);

  const codeExample = `import { motion } from 'framer-motion';

// Draggable Element
<motion.div
  drag
  dragConstraints={{ left: -100, right: 100, top: -50, bottom: 50 }}
  dragElastic={0.2}
  onDragEnd={() => setDragCount(prev => prev + 1)}
  className="animated-box"
>
  Drag Me!
</motion.div>

// Click and Hover Gestures
<motion.div
  whileHover={{ scale: 1.1, rotateZ: 5 }}
  whileTap={{ scale: 0.9, rotateZ: -5 }}
  className="animated-box"
>
  Interactive
</motion.div>`;

  return (
    <div className="card">
      <h2 className="section-header">4. Gesture Interactions</h2>
      <p className="demo-description">
        Advanced drag, hover, and tap gestures with constraints and elastic
        effects.
      </p>

      <div className="demo-grid">
        <DemoBox>
          <motion.div
            drag
            dragConstraints={{ left: -100, right: 100, top: -50, bottom: 50 }}
            dragElastic={0.2}
            onDragEnd={() => setDragCount((prev) => prev + 1)}
            className="animated-box"
            style={{ cursor: 'grab' }}>
            Drag Me!
          </motion.div>
          <p
            style={{
              marginTop: '1rem',
              fontSize: '0.75rem',
              color: 'var(--color-text-secondary)',
            }}>
            Dragged: {dragCount} times
          </p>
        </DemoBox>

        <DemoBox>
          <motion.div
            whileHover={{ scale: 1.1, rotateZ: 5 }}
            whileTap={{ scale: 0.9, rotateZ: -5 }}
            className="animated-box">
            Interactive
          </motion.div>
        </DemoBox>

        <DemoBox>
          <motion.div
            whileHover={{
              scale: 1.05,
              boxShadow: '0 10px 25px rgba(0, 212, 255, 0.3)',
            }}
            whileTap={{ scale: 0.95 }}
            className="animated-box"
            style={{
              background: 'linear-gradient(45deg, #00d4ff, #8b5cf6)',
              border: 'none',
            }}>
            Glow Effect
          </motion.div>
        </DemoBox>
      </div>

      <button onClick={() => setShowCode(!showCode)} className="show-code-btn">
        {showCode ? 'Hide Code' : 'Show Code'}
      </button>

      {showCode && (
        <CodeViewer
          code={codeExample}
          title="Gesture Interactions - React/Framer Motion"
        />
      )}
    </div>
  );
};

export default GestureInteractions;
