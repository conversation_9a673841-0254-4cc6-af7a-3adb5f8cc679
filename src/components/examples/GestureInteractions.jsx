import { motion } from 'framer-motion';
import { useState } from 'react';
import <PERSON><PERSON>ie<PERSON> from '../CodeViewer';
import DemoBox from '../DemoBox';

const GestureInteractions = () => {
  const [showCode, setShowCode] = useState(false);

  const codeExample = `// Advanced Drag with Swipe Detection
function DraggableCard() {
  const [dragCount, setDragCount] = useState(0);

  return (
    <motion.div
      className="drag-card"
      drag
      dragConstraints={{ left: -100, right: 100, top: -100, bottom: 100 }}
      dragElastic={0.1}
      whileDrag={{ scale: 1.05, rotate: 5 }}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onDragEnd={(event, info) => {
        if (Math.abs(info.offset.x) > 50) {
          setDragCount((prev) => prev + 1);
        }
      }}>
      <h3 style={{ marginBottom: '0.5rem', color: 'var(--color-text)' }}>
        Advanced Drag! 🎮
      </h3>
      <p
        style={{
          color: 'var(--color-text-secondary)',
          fontSize: '0.875rem',
        }}>
        Swipes detected: {dragCount}
      </p>
      <small style={{ color: 'var(--color-text-secondary)' }}>
        Drag beyond 50px to trigger swipe
      </small>
    </motion.div>
  );
}`;

  // Advanced Drag with Swipe Detection - Código exato do motion.html
  function DraggableCard() {
    const [dragCount, setDragCount] = useState(0);

    return (
      <motion.div
        className="drag-card"
        drag
        dragConstraints={{ left: -100, right: 100, top: -100, bottom: 100 }}
        dragElastic={0.1}
        whileDrag={{ scale: 1.05, rotate: 5 }}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        onDragEnd={(event, info) => {
          if (Math.abs(info.offset.x) > 50) {
            setDragCount((prev) => prev + 1);
          }
        }}>
        <h3
          style={{
            marginBottom: '0.5rem',
            color: 'var(--color-text)',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
          }}>
          <Gamepad2 size={20} />
          Advanced Drag!
        </h3>
        <p
          style={{
            color: 'var(--color-text-secondary)',
            fontSize: '0.875rem',
          }}>
          Swipes detected: {dragCount}
        </p>
        <small style={{ color: 'var(--color-text-secondary)' }}>
          Drag beyond 50px to trigger swipe
        </small>
      </motion.div>
    );
  }

  return (
    <div className="card">
      <h2 className="section-header">4. Advanced Gesture Interactions</h2>
      <p className="demo-description">
        Rich gesture support with drag constraints, swipe detection, and visual
        feedback.
      </p>

      <div className="demo-grid">
        <DemoBox>
          <DraggableCard />
        </DemoBox>
      </div>

      <button onClick={() => setShowCode(!showCode)} className="show-code-btn">
        {showCode ? 'Hide Code' : 'Show Code'}
      </button>

      {showCode && (
        <CodeViewer
          code={codeExample}
          title="Gesture Interactions - React/Framer Motion"
        />
      )}
    </div>
  );
};

export default GestureInteractions;
