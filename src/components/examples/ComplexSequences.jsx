import { motion } from 'framer-motion';
import { useState } from 'react';
import <PERSON><PERSON><PERSON><PERSON> from '../CodeViewer';
import DemoBox from '../DemoBox';

const ComplexSequences = () => {
  const [showCode, setShowCode] = useState(false);
  const [triggerSequence, setTriggerSequence] = useState(0);

  const sequenceVariants = {
    initial: { scale: 1, rotate: 0, backgroundColor: '#00d4ff' },
    animate: {
      scale: [1, 1.2, 0.8, 1.1, 1],
      rotate: [0, 90, 180, 270, 360],
      backgroundColor: ['#00d4ff', '#8b5cf6', '#00ff88', '#00d4ff'],
      transition: {
        duration: 3,
        times: [0, 0.2, 0.5, 0.8, 1],
        ease: 'easeInOut',
      },
    },
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { y: 0, opacity: 1 },
  };

  const codeExample = `import { motion } from 'framer-motion';

const sequenceVariants = {
  initial: { scale: 1, rotate: 0, backgroundColor: "#00d4ff" },
  animate: {
    scale: [1, 1.2, 0.8, 1.1, 1],
    rotate: [0, 90, 180, 270, 360],
    backgroundColor: ["#00d4ff", "#8b5cf6", "#00ff88", "#00d4ff"],
    transition: {
      duration: 3,
      times: [0, 0.2, 0.5, 0.8, 1],
      ease: "easeInOut"
    }
  }
};

// Staggered Animation
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1
    }
  }
};`;

  return (
    <div className="card">
      <h2 className="section-header">2. Complex Sequences</h2>
      <p className="demo-description">
        Advanced animation sequences with keyframes, staggered animations, and
        orchestration.
      </p>

      <div className="demo-grid">
        <DemoBox>
          <motion.div
            key={triggerSequence}
            variants={sequenceVariants}
            initial="initial"
            animate="animate"
            className="animated-box"
            style={{ borderRadius: '8px' }}>
            Complex
          </motion.div>
          <button
            onClick={() => setTriggerSequence((prev) => prev + 1)}
            className="btn-secondary"
            style={{ marginTop: '1rem', fontSize: '0.75rem' }}>
            Trigger Sequence
          </button>
        </DemoBox>

        <DemoBox>
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            style={{ display: 'flex', gap: '8px' }}>
            {[1, 2, 3, 4].map((item) => (
              <motion.div
                key={item}
                variants={itemVariants}
                className="animated-box"
                style={{
                  width: '40px',
                  height: '40px',
                  fontSize: '12px',
                }}>
                {item}
              </motion.div>
            ))}
          </motion.div>
        </DemoBox>

        <DemoBox>
          <motion.div
            animate={{
              pathLength: [0, 1, 0],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut',
            }}>
            <svg width="80" height="80" viewBox="0 0 100 100">
              <motion.circle
                cx="50"
                cy="50"
                r="40"
                stroke="#00d4ff"
                strokeWidth="4"
                fill="none"
                pathLength="0"
              />
            </svg>
          </motion.div>
        </DemoBox>
      </div>

      <button onClick={() => setShowCode(!showCode)} className="show-code-btn">
        {showCode ? 'Hide Code' : 'Show Code'}
      </button>

      {showCode && (
        <CodeViewer
          code={codeExample}
          title="Complex Sequences - React/Framer Motion"
        />
      )}
    </div>
  );
};

export default ComplexSequences;
