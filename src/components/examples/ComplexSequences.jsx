import { AnimatePresence, motion } from 'framer-motion';
import { useState } from 'react';
import Code<PERSON>iewer from '../CodeViewer';
import DemoBox from '../DemoBox';

const ComplexSequences = () => {
  const [showCode, setShowCode] = useState(false);

  const codeExample = `import { motion, AnimatePresence } from 'framer-motion';

// Staggered List Animation
function StaggeredList() {
  const [isVisible, setIsVisible] = useState(false);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: 'spring', stiffness: 100 },
    },
  };

  return (
    <div>
      <button onClick={() => setIsVisible(!isVisible)}>
        {isVisible ? 'Hide' : 'Show'} List
      </button>
      <AnimatePresence>
        {isVisible && (
          <motion.ul
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            exit="hidden"
            style={{ listStyle: 'none', padding: 0 }}
          >
            {['First Item', 'Second Item', 'Third Item', 'Fourth Item'].map((item, index) => (
              <motion.li
                key={index}
                variants={itemVariants}
                className="list-item"
              >
                {item}
              </motion.li>
            ))}
          </motion.ul>
        )}
      </AnimatePresence>
    </div>
  );
}`;

  // Staggered List Animation
  function StaggeredList() {
    const [isVisible, setIsVisible] = useState(false);

    const containerVariants = {
      hidden: { opacity: 0 },
      visible: {
        opacity: 1,
        transition: {
          delayChildren: 0.3,
          staggerChildren: 0.2,
        },
      },
    };

    const itemVariants = {
      hidden: { y: 20, opacity: 0 },
      visible: {
        y: 0,
        opacity: 1,
        transition: { type: 'spring', stiffness: 100 },
      },
    };

    return (
      <div>
        <button
          className="btn-primary"
          onClick={() => setIsVisible(!isVisible)}>
          {isVisible ? 'Hide' : 'Show'} List
        </button>
        <AnimatePresence>
          {isVisible && (
            <motion.ul
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              exit="hidden"
              style={{ listStyle: 'none', padding: 0, marginTop: '1rem' }}>
              {['First Item', 'Second Item', 'Third Item', 'Fourth Item'].map(
                (item, index) => (
                  <motion.li
                    key={index}
                    variants={itemVariants}
                    className="list-item">
                    {item}
                  </motion.li>
                )
              )}
            </motion.ul>
          )}
        </AnimatePresence>
      </div>
    );
  }

  return (
    <div className="card">
      <h2 className="section-header">2. Complex Sequences</h2>
      <p className="demo-description">
        Orchestrating multiple elements with staggered timing and child
        animations.
      </p>

      <div className="demo-grid">
        <DemoBox>
          <StaggeredList />
        </DemoBox>
      </div>

      <button onClick={() => setShowCode(!showCode)} className="show-code-btn">
        {showCode ? 'Hide Code' : 'Show Code'}
      </button>

      {showCode && (
        <CodeViewer
          code={codeExample}
          title="Complex Sequences - React/Framer Motion"
        />
      )}
    </div>
  );
};

export default ComplexSequences;
