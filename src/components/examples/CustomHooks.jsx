import { motion, useAnimation } from 'framer-motion';
import { useState, useCallback } from 'react';
import <PERSON><PERSON>iewer from '../CodeViewer';
import DemoBox from '../DemoBox';

const CustomHooks = () => {
  const [showCode, setShowCode] = useState(false);

  const codeExample = `import { motion, useAnimation } from 'framer-motion';
import { useCallback } from 'react';

// Custom Animation Hook
function useStaggeredAnimation(count, delay = 0.1) {
  const controls = useAnimation();

  const staggerAnimation = useCallback(async () => {
    await controls.start((i) => ({
      opacity: 1,
      y: 0,
      scale: 1,
      transition: { 
        delay: i * delay, 
        type: 'spring', 
        stiffness: 100 
      },
    }));
  }, [controls, delay]);

  const resetAnimation = useCallback(async () => {
    await controls.start((i) => ({
      opacity: 0,
      y: 20,
      scale: 0.8,
      transition: { delay: i * 0.05 },
    }));
  }, [controls]);

  return { controls, staggerAnimation, resetAnimation };
}

// Usage
function CustomHookDemo() {
  const { controls, staggerAnimation, resetAnimation } =
    useStaggeredAnimation(4, 0.15);

  return (
    <div>
      <div style={{ display: 'flex', gap: '0.5rem' }}>
        <button onClick={staggerAnimation}>Animate</button>
        <button onClick={resetAnimation}>Reset</button>
      </div>
      <div style={{ display: 'flex', gap: '0.5rem' }}>
        {[0, 1, 2, 3].map((i) => (
          <motion.div
            key={i}
            custom={i}
            animate={controls}
            initial={{ opacity: 0, y: 20, scale: 0.8 }}
            style={{
              width: '40px',
              height: '40px',
              backgroundColor: 'var(--color-accent-purple)',
              borderRadius: '8px',
            }}
          >
            {i + 1}
          </motion.div>
        ))}
      </div>
    </div>
  );
}`;

  // Custom Animation Hook
  function useStaggeredAnimation(count, delay = 0.1) {
    const controls = useAnimation();

    const staggerAnimation = useCallback(async () => {
      await controls.start((i) => ({
        opacity: 1,
        y: 0,
        scale: 1,
        transition: { delay: i * delay, type: 'spring', stiffness: 100 },
      }));
    }, [controls, delay]);

    const resetAnimation = useCallback(async () => {
      await controls.start((i) => ({
        opacity: 0,
        y: 20,
        scale: 0.8,
        transition: { delay: i * 0.05 },
      }));
    }, [controls]);

    return { controls, staggerAnimation, resetAnimation };
  }

  // Custom Hook Demo
  function CustomHookDemo() {
    const { controls, staggerAnimation, resetAnimation } =
      useStaggeredAnimation(4, 0.15);

    return (
      <div>
        <div
          style={{ display: 'flex', gap: '0.5rem', marginBottom: '1rem' }}>
          <button className="btn-primary" onClick={staggerAnimation}>
            Animate
          </button>
          <button className="btn-secondary" onClick={resetAnimation}>
            Reset
          </button>
        </div>
        <div style={{ display: 'flex', gap: '0.5rem', flexWrap: 'wrap' }}>
          {[0, 1, 2, 3].map((i) => (
            <motion.div
              key={i}
              custom={i}
              animate={controls}
              initial={{ opacity: 0, y: 20, scale: 0.8 }}
              style={{
                width: '40px',
                height: '40px',
                backgroundColor: 'var(--color-accent-purple)',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontWeight: 'bold',
              }}>
              {i + 1}
            </motion.div>
          ))}
        </div>
        <p className="demo-description">
          Custom useStaggeredAnimation hook
        </p>
      </div>
    );
  }

  // Reusable Bounce Hook
  function useBounceAnimation() {
    const controls = useAnimation();

    const bounce = useCallback(() => {
      controls.start({
        y: [0, -30, 0],
        transition: {
          duration: 0.6,
          times: [0, 0.5, 1],
          ease: "easeOut"
        }
      });
    }, [controls]);

    return { controls, bounce };
  }

  function BounceDemo() {
    const { controls, bounce } = useBounceAnimation();

    return (
      <div>
        <button className="btn-secondary" onClick={bounce}>
          Bounce!
        </button>
        <motion.div
          animate={controls}
          className="animated-box"
          style={{ marginTop: '1rem' }}>
          🏀
        </motion.div>
        <p className="demo-description">
          Reusable bounce animation hook
        </p>
      </div>
    );
  }

  return (
    <div className="card">
      <h2 className="section-header">8. Custom Animation Hooks</h2>
      <p className="demo-description">
        Create reusable animation logic with custom hooks for staggered animations, 
        controls, and complex sequences.
      </p>

      <div className="demo-grid">
        <DemoBox>
          <CustomHookDemo />
        </DemoBox>

        <DemoBox>
          <BounceDemo />
        </DemoBox>
      </div>

      <button onClick={() => setShowCode(!showCode)} className="show-code-btn">
        {showCode ? 'Hide Code' : 'Show Code'}
      </button>

      {showCode && (
        <CodeViewer
          code={codeExample}
          title="Custom Animation Hooks - React/Framer Motion"
        />
      )}
    </div>
  );
};

export default CustomHooks;
