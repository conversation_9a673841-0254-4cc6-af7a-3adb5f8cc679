import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import DemoBox from '../DemoBox';
import CodeViewer from '../CodeViewer';

const LayoutAnimations = () => {
  const [showCode, setShowCode] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedId, setSelectedId] = useState(null);

  const items = [
    { id: 1, title: 'Item 1', color: '#00d4ff' },
    { id: 2, title: 'Item 2', color: '#8b5cf6' },
    { id: 3, title: 'Item 3', color: '#00ff88' },
  ];

  const codeExample = `import { motion, AnimatePresence } from 'framer-motion';

// Layout Animation
<motion.div
  layout
  onClick={() => setIsExpanded(!isExpanded)}
  className="animated-box"
  style={{
    width: isExpanded ? '200px' : '100px',
    height: isExpanded ? '200px' : '100px'
  }}
>
  Click to {isExpanded ? 'Shrink' : 'Expand'}
</motion.div>

// Shared Layout Animation
{items.map(item => (
  <motion.div
    key={item.id}
    layoutId={item.id}
    onClick={() => setSelectedId(item.id)}
    whileHover={{ scale: 1.05 }}
  >
    {item.title}
  </motion.div>
))}`;

  return (
    <div className="card">
      <h2 className="section-header">5. Layout Animations</h2>
      <p className="demo-description">
        Automatic layout animations and shared element transitions between
        states.
      </p>

      <div className="demo-grid">
        <DemoBox>
          <motion.div
            layout
            onClick={() => setIsExpanded(!isExpanded)}
            className="animated-box"
            style={{
              width: isExpanded ? '120px' : '80px',
              height: isExpanded ? '120px' : '80px',
              cursor: 'pointer',
              fontSize: isExpanded ? '12px' : '14px',
            }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}>
            Click to {isExpanded ? 'Shrink' : 'Expand'}
          </motion.div>
        </DemoBox>

        <DemoBox>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            {items.map((item) => (
              <motion.div
                key={item.id}
                layoutId={`item-${item.id}`}
                onClick={() =>
                  setSelectedId(selectedId === item.id ? null : item.id)
                }
                whileHover={{ scale: 1.05 }}
                style={{
                  padding: '8px 16px',
                  backgroundColor: item.color,
                  borderRadius: '8px',
                  cursor: 'pointer',
                  color: 'white',
                  fontSize: '12px',
                  fontWeight: '600',
                }}>
                {item.title}
              </motion.div>
            ))}
          </div>
        </DemoBox>

        <DemoBox>
          <motion.div
            animate={{
              borderRadius: isExpanded ? '50%' : '8px',
              rotate: isExpanded ? 180 : 0,
            }}
            transition={{ duration: 0.5 }}
            className="animated-box"
            style={{ cursor: 'pointer' }}
            onClick={() => setIsExpanded(!isExpanded)}>
            {isExpanded ? '●' : '▲'}
          </motion.div>
        </DemoBox>
      </div>

      <AnimatePresence>
        {selectedId && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            style={{
              position: 'fixed',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              backgroundColor: 'var(--color-card)',
              padding: '2rem',
              borderRadius: '1rem',
              border: '1px solid var(--color-border)',
              zIndex: 1000,
            }}
            onClick={() => setSelectedId(null)}>
            <motion.div
              layoutId={`item-${selectedId}`}
              style={{
                backgroundColor: items.find((item) => item.id === selectedId)
                  ?.color,
                padding: '1rem 2rem',
                borderRadius: '8px',
                color: 'white',
                fontWeight: '600',
              }}>
              {items.find((item) => item.id === selectedId)?.title} - Expanded
              View
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      <button onClick={() => setShowCode(!showCode)} className="show-code-btn">
        {showCode ? 'Hide Code' : 'Show Code'}
      </button>

      {showCode && (
        <CodeViewer
          code={codeExample}
          title="Layout Animations - React/Framer Motion"
        />
      )}
    </div>
  );
};

export default LayoutAnimations;
