import { motion } from 'framer-motion';
import { useState } from 'react';
import Code<PERSON>iewer from '../CodeViewer';
import DemoBox from '../DemoBox';

const BasicAnimations = () => {
  const [showCode, setShowCode] = useState(false);

  const codeExample = `import { motion } from 'framer-motion';

// Basic Animation Component
function BasicAnimation() {
  return (
    <motion.div
      className="animated-box"
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      ✨
    </motion.div>
  );
}`;

  // Basic Animation Component
  function BasicAnimation() {
    return (
      <motion.div
        className="animated-box"
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}>
        ✨
      </motion.div>
    );
  }

  return (
    <div className="card">
      <h2 className="section-header">1. Basic Animations</h2>
      <p className="demo-description">
        Fundamental concepts: initial state, animate target, and transition
        configuration.
      </p>

      <div className="demo-grid">
        <DemoBox>
          <BasicAnimation />
          <p className="demo-description">Simple entrance animation</p>
        </DemoBox>
      </div>

      <button onClick={() => setShowCode(!showCode)} className="show-code-btn">
        {showCode ? 'Hide Code' : 'Show Code'}
      </button>

      {showCode && (
        <CodeViewer
          code={codeExample}
          title="Basic Animations - React/Framer Motion"
        />
      )}
    </div>
  );
};

export default BasicAnimations;
