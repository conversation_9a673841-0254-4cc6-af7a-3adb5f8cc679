import { motion } from 'framer-motion';
import { useState } from 'react';
import <PERSON><PERSON><PERSON><PERSON> from '../CodeViewer';
import De<PERSON>Box from '../DemoBox';

const BasicAnimations = () => {
  const [showCode, setShowCode] = useState(false);

  const codeExample = `import { motion } from 'framer-motion';

// Basic Animation Component
function BasicAnimation() {
  return (
    <motion.div
      className="animated-box"
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      ✨
    </motion.div>
  );
}

// Hover and Tap Interactions
<motion.div
  whileHover={{ scale: 1.1 }}
  whileTap={{ scale: 0.9 }}
  className="animated-box"
>
  Interactive
</motion.div>

// Continuous Rotation
<motion.div
  animate={{ rotate: 360 }}
  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
  className="animated-box"
>
  🌟
</motion.div>`;

  // Basic Animation Component
  function BasicAnimation() {
    return (
      <motion.div
        className="animated-box"
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}>
        ✨
      </motion.div>
    );
  }

  return (
    <div className="card">
      <h2 className="section-header">1. Basic Animations</h2>
      <p className="demo-description">
        Fundamental concepts: initial state, animate target, and transition
        configuration.
      </p>

      <div className="demo-grid">
        <DemoBox>
          <BasicAnimation />
          <p className="demo-description">Simple entrance animation</p>
        </DemoBox>

        <DemoBox>
          <motion.div
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className="animated-box">
            Interactive
          </motion.div>
          <p className="demo-description">Hover and tap interactions</p>
        </DemoBox>

        <DemoBox>
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
            className="animated-box">
            🌟
          </motion.div>
          <p className="demo-description">Continuous rotation</p>
        </DemoBox>
      </div>

      <button onClick={() => setShowCode(!showCode)} className="show-code-btn">
        {showCode ? 'Hide Code' : 'Show Code'}
      </button>

      {showCode && (
        <CodeViewer
          code={codeExample}
          title="Basic Animations - React/Framer Motion"
        />
      )}
    </div>
  );
};

export default BasicAnimations;
