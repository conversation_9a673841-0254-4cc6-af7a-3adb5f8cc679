import { motion } from 'framer-motion';
import { useState } from 'react';
import <PERSON><PERSON><PERSON><PERSON> from '../CodeViewer';
import DemoBox from '../DemoBox';

const BasicAnimations = () => {
  const [showCode, setShowCode] = useState(false);

  const codeExample = `import { motion } from 'framer-motion';

// Hover Animation
<motion.div
  whileHover={{ scale: 1.1 }}
  whileTap={{ scale: 0.9 }}
  className="animated-box"
>
  Hover Me
</motion.div>

// Rotation Animation
<motion.div
  animate={{ rotate: 360 }}
  transition={{ duration: 2, repeat: Infinity }}
  className="animated-box"
>
  Spinning
</motion.div>`;

  return (
    <div className="card">
      <h2 className="section-header">1. Basic Animations</h2>
      <p className="demo-description">
        Fundamental Framer Motion animations with hover, tap, and rotate
        effects.
      </p>

      <div className="demo-grid">
        <DemoBox>
          <motion.div
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className="animated-box">
            Hover Me
          </motion.div>
        </DemoBox>

        <DemoBox>
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity }}
            className="animated-box">
            Spinning
          </motion.div>
        </DemoBox>

        <DemoBox>
          <motion.div
            animate={{ y: [-20, 20, -20] }}
            transition={{ duration: 1.5, repeat: Infinity }}
            className="animated-box">
            Bouncing
          </motion.div>
        </DemoBox>
      </div>

      <button onClick={() => setShowCode(!showCode)} className="show-code-btn">
        {showCode ? 'Hide Code' : 'Show Code'}
      </button>

      {showCode && (
        <CodeViewer
          code={codeExample}
          title="Basic Animations - React/Framer Motion"
        />
      )}
    </div>
  );
};

export default BasicAnimations;
