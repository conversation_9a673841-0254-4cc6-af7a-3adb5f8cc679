import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import <PERSON><PERSON>iewer from '../CodeViewer';
import DemoBox from '../DemoBox';

const PageTransitions = () => {
  const [showCode, setShowCode] = useState(false);

  const codeExample = `import { motion, AnimatePresence } from 'framer-motion';

// Page Transition Simulator
function PageTransitionDemo() {
  const [currentPage, setCurrentPage] = useState(0);
  const pages = ['Home', 'About', 'Services', 'Contact'];

  const pageVariants = {
    initial: { opacity: 0, x: '-100vw', scale: 0.8 },
    in: { opacity: 1, x: 0, scale: 1 },
    out: { opacity: 0, x: '100vw', scale: 1.2 },
  };

  const pageTransition = {
    type: 'tween',
    ease: 'anticipate',
    duration: 0.5,
  };

  return (
    <div>
      <div style={{ display: 'flex', gap: '0.5rem' }}>
        {pages.map((page, index) => (
          <button
            key={page}
            className={currentPage === index ? 'btn-primary' : 'btn-secondary'}
            onClick={() => setCurrentPage(index)}
          >
            {page}
          </button>
        ))}
      </div>
      
      <div style={{ height: '120px', overflow: 'hidden', position: 'relative' }}>
        <AnimatePresence mode="wait">
          <motion.div
            key={currentPage}
            initial="initial"
            animate="in"
            exit="out"
            variants={pageVariants}
            transition={pageTransition}
            style={{
              position: 'absolute',
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            {pages[currentPage]} Page
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
}`;

  // Page Transition Simulator
  function PageTransitionDemo() {
    const [currentPage, setCurrentPage] = useState(0);
    const pages = ['Home', 'About', 'Services', 'Contact'];

    const pageVariants = {
      initial: { opacity: 0, x: '-100vw', scale: 0.8 },
      in: { opacity: 1, x: 0, scale: 1 },
      out: { opacity: 0, x: '100vw', scale: 1.2 },
    };

    const pageTransition = {
      type: 'tween',
      ease: 'anticipate',
      duration: 0.5,
    };

    return (
      <div>
        <div
          style={{
            display: 'flex',
            gap: '0.5rem',
            marginBottom: '1rem',
            flexWrap: 'wrap',
          }}>
          {pages.map((page, index) => (
            <button
              key={page}
              className={
                currentPage === index ? 'btn-primary' : 'btn-secondary'
              }
              onClick={() => setCurrentPage(index)}>
              {page}
            </button>
          ))}
        </div>
        <div
          style={{
            height: '120px',
            backgroundColor: 'var(--color-card)',
            border: '1px solid var(--color-border)',
            borderRadius: '0.5rem',
            overflow: 'hidden',
            position: 'relative',
          }}>
          <AnimatePresence mode="wait">
            <motion.div
              key={currentPage}
              initial="initial"
              animate="in"
              exit="out"
              variants={pageVariants}
              transition={pageTransition}
              style={{
                position: 'absolute',
                width: '100%',
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: `linear-gradient(135deg, var(--color-accent-blue), var(--color-accent-purple))`,
                color: 'white',
                fontSize: '1.5rem',
                fontWeight: 'bold',
              }}>
              {pages[currentPage]} Page
            </motion.div>
          </AnimatePresence>
        </div>
        <p className="demo-description">
          Advanced page transitions with AnimatePresence
        </p>
      </div>
    );
  }

  // Slide Transition Demo
  function SlideTransitionDemo() {
    const [currentSlide, setCurrentSlide] = useState(0);
    const slides = ['🌟', '🚀', '🎯', '💎'];

    const slideVariants = {
      enter: (direction) => ({
        x: direction > 0 ? 1000 : -1000,
        opacity: 0
      }),
      center: {
        zIndex: 1,
        x: 0,
        opacity: 1
      },
      exit: (direction) => ({
        zIndex: 0,
        x: direction < 0 ? 1000 : -1000,
        opacity: 0
      })
    };

    const [direction, setDirection] = useState(0);

    const paginate = (newDirection) => {
      setDirection(newDirection);
      setCurrentSlide((prev) => {
        if (newDirection === 1) {
          return prev === slides.length - 1 ? 0 : prev + 1;
        } else {
          return prev === 0 ? slides.length - 1 : prev - 1;
        }
      });
    };

    return (
      <div>
        <div style={{ display: 'flex', gap: '0.5rem', marginBottom: '1rem' }}>
          <button className="btn-secondary" onClick={() => paginate(-1)}>
            ← Previous
          </button>
          <button className="btn-secondary" onClick={() => paginate(1)}>
            Next →
          </button>
        </div>
        <div
          style={{
            height: '120px',
            backgroundColor: 'var(--color-card)',
            border: '1px solid var(--color-border)',
            borderRadius: '0.5rem',
            overflow: 'hidden',
            position: 'relative',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <AnimatePresence initial={false} custom={direction}>
            <motion.div
              key={currentSlide}
              custom={direction}
              variants={slideVariants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{
                x: { type: "spring", stiffness: 300, damping: 30 },
                opacity: { duration: 0.2 }
              }}
              style={{
                position: 'absolute',
                fontSize: '3rem',
              }}>
              {slides[currentSlide]}
            </motion.div>
          </AnimatePresence>
        </div>
        <p className="demo-description">
          Directional slide transitions with spring physics
        </p>
      </div>
    );
  }

  return (
    <div className="card">
      <h2 className="section-header">9. Page Transitions</h2>
      <p className="demo-description">
        Advanced page transitions with AnimatePresence, custom easing functions, 
        and directional animations.
      </p>

      <div className="demo-grid">
        <DemoBox>
          <PageTransitionDemo />
        </DemoBox>

        <DemoBox>
          <SlideTransitionDemo />
        </DemoBox>
      </div>

      <button onClick={() => setShowCode(!showCode)} className="show-code-btn">
        {showCode ? 'Hide Code' : 'Show Code'}
      </button>

      {showCode && (
        <CodeViewer
          code={codeExample}
          title="Page Transitions - React/Framer Motion"
        />
      )}
    </div>
  );
};

export default PageTransitions;
