import { motion, useMotionValue, useTransform } from 'framer-motion';
import { useEffect, useMemo, useState } from 'react';
import CodeViewer from '../CodeViewer';
import DemoBox from '../DemoBox';

const PerformanceOptimizations = () => {
  const [showCode, setShowCode] = useState(false);

  const codeExample = `import { motion, useMotionValue, useTransform } from 'framer-motion';
import { useMemo, useCallback } from 'react';

// Performance Optimized Component with useMemo
function OptimizedAnimation({ isActive }) {
  const variants = useMemo(
    () => ({
      inactive: {
        opacity: 0.5,
        scale: 0.95,
        backgroundColor: 'var(--color-border)',
      },
      active: {
        opacity: 1,
        scale: 1,
        backgroundColor: 'var(--color-accent-blue)',
        transition: { duration: 0.3 },
      },
    }),
    []
  );

  return (
    <motion.div
      className="animated-box"
      variants={variants}
      animate={isActive ? 'active' : 'inactive'}
    >
      {isActive ? '🚀' : '💤'}
    </motion.div>
  );
}

// MotionValues for High Performance
function ScrollIndicator() {
  const scrollY = useMotionValue(0);
  
  useEffect(() => {
    const updateScrollY = () => {
      const progress = window.scrollY / 
        (document.body.scrollHeight - window.innerHeight);
      scrollY.set(Math.min(Math.max(progress * 100, 0), 100));
    };

    window.addEventListener('scroll', updateScrollY);
    return () => window.removeEventListener('scroll', updateScrollY);
  }, [scrollY]);

  return (
    <motion.div
      className="progress-bar"
      style={{
        width: useTransform(scrollY, [0, 100], ['0%', '100%']),
      }}
    />
  );
}`;

  // Performance Optimized Component with useMemo
  function OptimizedAnimation({ isActive }) {
    const variants = useMemo(
      () => ({
        inactive: {
          opacity: 0.5,
          scale: 0.95,
          backgroundColor: 'var(--color-border)',
        },
        active: {
          opacity: 1,
          scale: 1,
          backgroundColor: 'var(--color-accent-blue)',
          transition: { duration: 0.3 },
        },
      }),
      []
    );

    return (
      <motion.div
        className="animated-box"
        variants={variants}
        animate={isActive ? 'active' : 'inactive'}
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontWeight: 'bold',
        }}>
        {isActive ? '🚀' : '💤'}
      </motion.div>
    );
  }

  // Performance Demo Container - Auto Demo
  function PerformanceDemo() {
    const [isActive, setIsActive] = useState(false);

    // Auto demo - alterna automaticamente
    useEffect(() => {
      const interval = setInterval(() => {
        setIsActive((prev) => !prev);
      }, 2000);
      return () => clearInterval(interval);
    }, []);

    return (
      <div>
        <button
          className="btn-secondary"
          onClick={() => setIsActive(!isActive)}>
          {isActive ? 'Deactivate' : 'Activate'}
        </button>
        <div style={{ marginTop: '1rem' }}>
          <OptimizedAnimation isActive={isActive} />
        </div>
        <p className="demo-description">Optimized with useMemo variants</p>
      </div>
    );
  }

  // MotionValues and Scroll Indicator
  function ScrollIndicator() {
    const scrollY = useMotionValue(0);
    const [scrollProgress, setScrollProgress] = useState(0);

    useEffect(() => {
      const updateScrollY = () => {
        const progress =
          window.scrollY / (document.body.scrollHeight - window.innerHeight);
        const clampedProgress = Math.min(Math.max(progress * 100, 0), 100);
        scrollY.set(clampedProgress);
        setScrollProgress(clampedProgress);
      };

      window.addEventListener('scroll', updateScrollY);
      return () => window.removeEventListener('scroll', updateScrollY);
    }, [scrollY]);

    return (
      <div>
        <div className="progress-container">
          <motion.div
            className="progress-bar"
            style={{
              width: useTransform(scrollY, [0, 100], ['0%', '100%']),
            }}
          />
        </div>
        <p className="demo-description">
          Scroll progress: {Math.round(scrollProgress)}%
        </p>
        <small style={{ color: 'var(--color-text-secondary)' }}>
          Uses MotionValues for performance
        </small>
      </div>
    );
  }

  return (
    <div className="card">
      <h2 className="section-header">7. Performance Optimizations</h2>
      <p className="demo-description">
        Advanced techniques for optimizing Framer Motion animations using
        useMemo, MotionValues, and efficient state management.
      </p>

      <div className="demo-grid">
        <DemoBox>
          <PerformanceDemo />
        </DemoBox>

        <DemoBox>
          <ScrollIndicator />
        </DemoBox>
      </div>

      <button onClick={() => setShowCode(!showCode)} className="show-code-btn">
        {showCode ? 'Hide Code' : 'Show Code'}
      </button>

      {showCode && (
        <CodeViewer
          code={codeExample}
          title="Performance Optimizations - React/Framer Motion"
        />
      )}
    </div>
  );
};

export default PerformanceOptimizations;
