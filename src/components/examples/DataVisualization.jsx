import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TrendingUp } from 'lucide-react';
import { useEffect, useState } from 'react';
import CodeViewer from '../CodeViewer';
import DemoBox from '../DemoBox';

const DataVisualization = () => {
  const [showCode, setShowCode] = useState(false);

  const codeExample = `import { motion } from 'framer-motion';

// Animated Chart
function AnimatedChart() {
  const [animate, setAnimate] = useState(false);
  const data = [
    { id: 1, value: 65, label: 'A' },
    { id: 2, value: 80, label: 'B' },
    { id: 3, value: 45, label: 'C' },
    { id: 4, value: 90, label: 'D' },
    { id: 5, value: 70, label: 'E' },
  ];

  return (
    <div>
      <button onClick={() => setAnimate(!animate)}>
        {animate ? 'Reset' : 'Animate'} Chart
      </button>
      <div className="chart-container">
        {data.map((item, index) => (
          <motion.div
            key={item.id}
            className="bar"
            initial={{ height: 0 }}
            animate={{ height: animate ? \`\${item.value}%\` : 0 }}
            transition={{
              delay: index * 0.1,
              type: 'spring',
              stiffness: 100,
            }}
            whileHover={{ scale: 1.05 }}
          >
            {item.label}
          </motion.div>
        ))}
      </div>
    </div>
  );
}

// Animated Counter
function AnimatedCounter({ target }) {
  return (
    <motion.span
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <motion.span
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ type: "spring", stiffness: 100 }}
      >
        {target}
      </motion.span>
    </motion.span>
  );
}`;

  // Animated Chart - Auto Demo
  function AnimatedChart() {
    const [animate, setAnimate] = useState(false);
    const data = [
      { id: 1, value: 65, label: 'A' },
      { id: 2, value: 80, label: 'B' },
      { id: 3, value: 45, label: 'C' },
      { id: 4, value: 90, label: 'D' },
      { id: 5, value: 70, label: 'E' },
    ];

    // Auto demo - anima automaticamente
    useEffect(() => {
      const interval = setInterval(() => {
        setAnimate((prev) => !prev);
      }, 3000);
      return () => clearInterval(interval);
    }, []);

    return (
      <div>
        <button
          className="btn-secondary"
          onClick={() => setAnimate(!animate)}
          style={{
            marginBottom: '1rem',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
          }}>
          {animate ? <RotateCcw size={16} /> : <BarChart3 size={16} />}
          {animate ? 'Reset' : 'Animate'} Chart
        </button>
        <div className="chart-container">
          {data.map((item, index) => (
            <motion.div
              key={item.id}
              className="bar"
              initial={{ height: 0 }}
              animate={{ height: animate ? `${item.value}%` : 0 }}
              transition={{
                delay: index * 0.1,
                type: 'spring',
                stiffness: 100,
              }}
              whileHover={{ scale: 1.05 }}
              style={{
                display: 'flex',
                alignItems: 'end',
                justifyContent: 'center',
                color: 'white',
                fontWeight: 'bold',
                fontSize: '0.75rem',
                paddingBottom: '0.25rem',
              }}>
              {item.label}
            </motion.div>
          ))}
        </div>
      </div>
    );
  }

  // Animated Counter
  function AnimatedCounter() {
    const [count, setCount] = useState(0);
    const target = 1234;

    const startCount = () => {
      setCount(0);
      const increment = target / 100;
      const timer = setInterval(() => {
        setCount((prev) => {
          const next = prev + increment;
          if (next >= target) {
            clearInterval(timer);
            return target;
          }
          return next;
        });
      }, 20);
    };

    return (
      <div>
        <button
          className="btn-secondary"
          onClick={startCount}
          style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
          <TrendingUp size={16} />
          Start Counter
        </button>
        <motion.div
          className="animated-box"
          style={{
            fontSize: '1.5rem',
            fontWeight: 'bold',
            marginTop: '1rem',
          }}>
          <motion.span
            key={Math.floor(count)}
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ type: 'spring', stiffness: 200 }}>
            {Math.floor(count)}
          </motion.span>
        </motion.div>
        <p className="demo-description">Animated counter</p>
      </div>
    );
  }

  // Pie Chart Animation - Auto Demo
  function PieChartAnimation() {
    const [animate, setAnimate] = useState(false);

    // Auto demo - anima automaticamente
    useEffect(() => {
      const interval = setInterval(() => {
        setAnimate((prev) => !prev);
      }, 4000);
      return () => clearInterval(interval);
    }, []);

    return (
      <div>
        <button
          className="btn-secondary"
          onClick={() => setAnimate(!animate)}
          style={{
            marginBottom: '1rem',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
          }}>
          {animate ? <RotateCcw size={16} /> : <PieChart size={16} />}
          {animate ? 'Reset' : 'Animate'} Pie
        </button>
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          <svg width="80" height="80" viewBox="0 0 100 100">
            <motion.circle
              cx="50"
              cy="50"
              r="40"
              stroke="var(--color-accent-blue)"
              strokeWidth="8"
              fill="none"
              strokeDasharray="251.2"
              initial={{ strokeDashoffset: 251.2 }}
              animate={{
                strokeDashoffset: animate ? 125.6 : 251.2,
                rotate: animate ? 360 : 0,
              }}
              transition={{
                duration: 2,
                type: 'spring',
                stiffness: 100,
              }}
              style={{ transformOrigin: '50% 50%' }}
            />
          </svg>
        </div>
        <p className="demo-description">Animated pie chart</p>
      </div>
    );
  }

  return (
    <div className="card">
      <h2 className="section-header">12. Data Visualization</h2>
      <p className="demo-description">
        Interactive charts with staggered animations, hover interactions, and
        manual controls.
      </p>

      <div className="demo-grid">
        <DemoBox>
          <AnimatedChart />
        </DemoBox>

        <DemoBox>
          <AnimatedCounter />
        </DemoBox>

        <DemoBox>
          <PieChartAnimation />
        </DemoBox>
      </div>

      <button onClick={() => setShowCode(!showCode)} className="show-code-btn">
        {showCode ? 'Hide Code' : 'Show Code'}
      </button>

      {showCode && (
        <CodeViewer
          code={codeExample}
          title="Data Visualization - React/Framer Motion"
        />
      )}
    </div>
  );
};

export default DataVisualization;
