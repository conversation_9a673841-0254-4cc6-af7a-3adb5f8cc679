import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';
import Code<PERSON>iewer from '../CodeViewer';
import DemoBox from '../DemoBox';

const AccessibilityAnimations = () => {
  const [showCode, setShowCode] = useState(false);

  const codeExample = `import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';

// Accessibility-Aware Animation
function AccessibleAnimation() {
  const [shouldReduceMotion, setShouldReduceMotion] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  // Simulate prefers-reduced-motion detection
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setShouldReduceMotion(mediaQuery.matches);

    const handler = (e) => setShouldReduceMotion(e.matches);
    mediaQuery.addEventListener('change', handler);
    return () => mediaQuery.removeEventListener('change', handler);
  }, []);

  const triggerAnimation = () => {
    setIsAnimating(true);
    setTimeout(() => setIsAnimating(false), 1000);
  };

  return (
    <div>
      <button onClick={triggerAnimation}>
        Trigger Animation
      </button>
      <label>
        <input
          type="checkbox"
          checked={shouldReduceMotion}
          onChange={(e) => setShouldReduceMotion(e.target.checked)}
        />
        Reduce motion (simulate accessibility preference)
      </label>
      <motion.div
        className="animated-box"
        animate={
          isAnimating
            ? {
                x: shouldReduceMotion ? 0 : 100,
                rotate: shouldReduceMotion ? 0 : 360,
                scale: shouldReduceMotion ? 1 : 1.2,
              }
            : {
                x: 0,
                rotate: 0,
                scale: 1,
              }
        }
        transition={{
          duration: shouldReduceMotion ? 0.1 : 0.8,
          type: shouldReduceMotion ? 'tween' : 'spring',
        }}
      >
        ♿
      </motion.div>
    </div>
  );
}`;

  // Accessibility-Aware Animation
  function AccessibleAnimation() {
    const [shouldReduceMotion, setShouldReduceMotion] = useState(false);
    const [isAnimating, setIsAnimating] = useState(false);

    // Simulate prefers-reduced-motion detection
    useEffect(() => {
      const mediaQuery = window.matchMedia(
        '(prefers-reduced-motion: reduce)'
      );
      setShouldReduceMotion(mediaQuery.matches);

      const handler = (e) => setShouldReduceMotion(e.matches);
      mediaQuery.addEventListener('change', handler);
      return () => mediaQuery.removeEventListener('change', handler);
    }, []);

    const triggerAnimation = () => {
      setIsAnimating(true);
      setTimeout(() => setIsAnimating(false), 1000);
    };

    return (
      <div>
        <div style={{ marginBottom: '1rem' }}>
          <button className="btn-primary" onClick={triggerAnimation}>
            Trigger Animation
          </button>
          <label
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              marginTop: '0.5rem',
              color: 'var(--color-text-secondary)',
              fontSize: '0.875rem',
            }}>
            <input
              type="checkbox"
              checked={shouldReduceMotion}
              onChange={(e) => setShouldReduceMotion(e.target.checked)}
            />
            Reduce motion (simulate accessibility preference)
          </label>
        </div>
        <motion.div
          className="animated-box"
          animate={
            isAnimating
              ? {
                  x: shouldReduceMotion ? 0 : 100,
                  rotate: shouldReduceMotion ? 0 : 360,
                  scale: shouldReduceMotion ? 1 : 1.2,
                }
              : {
                  x: 0,
                  rotate: 0,
                  scale: 1,
                }
          }
          transition={{
            duration: shouldReduceMotion ? 0.1 : 0.8,
            type: shouldReduceMotion ? 'tween' : 'spring',
          }}>
          ♿
        </motion.div>
        <p className="demo-description">
          Respects reduced motion preferences
        </p>
      </div>
    );
  }

  return (
    <div className="card">
      <h2 className="section-header">10. Accessibility Considerations</h2>
      <p className="demo-description">
        Respecting user preferences for reduced motion and providing accessible alternatives.
      </p>

      <div className="demo-grid">
        <DemoBox>
          <AccessibleAnimation />
        </DemoBox>
      </div>

      <button onClick={() => setShowCode(!showCode)} className="show-code-btn">
        {showCode ? 'Hide Code' : 'Show Code'}
      </button>

      {showCode && (
        <CodeViewer
          code={codeExample}
          title="Accessibility Animations - React/Framer Motion"
        />
      )}
    </div>
  );
};

export default AccessibilityAnimations;
