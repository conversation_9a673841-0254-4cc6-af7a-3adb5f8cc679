import { AnimatePresence, motion } from 'framer-motion';
import { useEffect, useState } from 'react';
import <PERSON><PERSON>iewer from '../CodeViewer';
import DemoBox from '../DemoBox';

const ModalsOverlays = () => {
  const [showCode, setShowCode] = useState(false);

  const codeExample = `import { motion, AnimatePresence } from 'framer-motion';

// Animated Modal
function AnimatedModal() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div>
      <button onClick={() => setIsOpen(true)}>
        Open Modal
      </button>
      <AnimatePresence>
        {isOpen && (
          <>
            <motion.div
              className="modal-backdrop"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setIsOpen(false)}
            />
            <motion.div
              className="modal"
              initial={{ opacity: 0, scale: 0.8, y: 50 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.8, y: 50 }}
              transition={{
                type: 'spring',
                damping: 25,
                stiffness: 500,
              }}
            >
              <h3>Advanced Modal 🎭</h3>
              <p>This modal uses smooth enter and exit animations with spring physics.</p>
              <button onClick={() => setIsOpen(false)}>
                Close Modal
              </button>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
}`;

  // Animated Modal
  function AnimatedModal() {
    const [isOpen, setIsOpen] = useState(false);

    return (
      <div>
        <button className="btn-primary" onClick={() => setIsOpen(true)}>
          Open Modal
        </button>
        <AnimatePresence>
          {isOpen && (
            <>
              <motion.div
                className="modal-backdrop"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                onClick={() => setIsOpen(false)}
              />
              <motion.div
                className="modal"
                initial={{ opacity: 0, scale: 0.8, y: 50 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.8, y: 50 }}
                transition={{
                  type: 'spring',
                  damping: 25,
                  stiffness: 500,
                }}>
                <h3
                  style={{
                    color: 'var(--color-text)',
                    marginBottom: '1rem',
                    fontSize: '1.5rem',
                  }}>
                  Advanced Modal 🎭
                </h3>
                <p
                  style={{
                    color: 'var(--color-text-secondary)',
                    marginBottom: '1.5rem',
                  }}>
                  This modal uses smooth enter and exit animations with spring
                  physics. Click the backdrop or close button to dismiss.
                </p>
                <button
                  className="btn-primary"
                  onClick={() => setIsOpen(false)}>
                  Close Modal
                </button>
              </motion.div>
            </>
          )}
        </AnimatePresence>
      </div>
    );
  }

  // Slide-in Drawer
  function SlideDrawer() {
    const [isOpen, setIsOpen] = useState(false);

    return (
      <div>
        <button className="btn-secondary" onClick={() => setIsOpen(true)}>
          Open Drawer
        </button>
        <AnimatePresence>
          {isOpen && (
            <>
              <motion.div
                className="modal-backdrop"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                onClick={() => setIsOpen(false)}
              />
              <motion.div
                initial={{ x: '100%' }}
                animate={{ x: 0 }}
                exit={{ x: '100%' }}
                transition={{ type: 'spring', damping: 30, stiffness: 300 }}
                style={{
                  position: 'fixed',
                  top: 0,
                  right: 0,
                  width: '300px',
                  height: '100vh',
                  backgroundColor: 'var(--color-card)',
                  border: '1px solid var(--color-border)',
                  padding: '2rem',
                  zIndex: 1001,
                }}>
                <h3
                  style={{ color: 'var(--color-text)', marginBottom: '1rem' }}>
                  Slide Drawer
                </h3>
                <p
                  style={{
                    color: 'var(--color-text-secondary)',
                    marginBottom: '1.5rem',
                  }}>
                  This drawer slides in from the right side with spring physics.
                </p>
                <button
                  className="btn-secondary"
                  onClick={() => setIsOpen(false)}>
                  Close
                </button>
              </motion.div>
            </>
          )}
        </AnimatePresence>
      </div>
    );
  }

  // Tooltip Animation - Auto Demo
  function TooltipDemo() {
    const [showTooltip, setShowTooltip] = useState(false);

    // Auto demo - mostra tooltip automaticamente
    useEffect(() => {
      const interval = setInterval(() => {
        setShowTooltip((prev) => !prev);
      }, 2000);
      return () => clearInterval(interval);
    }, []);

    return (
      <div style={{ position: 'relative', display: 'inline-block' }}>
        <motion.button
          className="btn-secondary"
          onHoverStart={() => setShowTooltip(true)}
          onHoverEnd={() => setShowTooltip(false)}
          whileHover={{ scale: 1.05 }}
          animate={{ scale: showTooltip ? 1.05 : 1 }}>
          Auto Tooltip Demo
        </motion.button>
        <AnimatePresence>
          {showTooltip && (
            <motion.div
              initial={{ opacity: 0, y: 10, scale: 0.8 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: 10, scale: 0.8 }}
              transition={{ duration: 0.2 }}
              style={{
                position: 'absolute',
                bottom: '100%',
                left: '50%',
                transform: 'translateX(-50%)',
                marginBottom: '8px',
                padding: '8px 12px',
                backgroundColor: 'var(--color-text)',
                color: 'var(--color-bg)',
                borderRadius: '4px',
                fontSize: '0.875rem',
                whiteSpace: 'nowrap',
                zIndex: 1000,
              }}>
              Auto tooltip demo!
              <div
                style={{
                  position: 'absolute',
                  top: '100%',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: 0,
                  height: 0,
                  borderLeft: '4px solid transparent',
                  borderRight: '4px solid transparent',
                  borderTop: '4px solid var(--color-text)',
                }}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  }

  return (
    <div className="card">
      <h2 className="section-header">13. Modals & Overlays</h2>
      <p className="demo-description">
        Smooth transitions for elements that enter and exit the DOM using
        AnimatePresence.
      </p>

      <div className="demo-grid">
        <DemoBox>
          <AnimatedModal />
        </DemoBox>

        <DemoBox>
          <SlideDrawer />
        </DemoBox>

        <DemoBox>
          <TooltipDemo />
        </DemoBox>
      </div>

      <button onClick={() => setShowCode(!showCode)} className="show-code-btn">
        {showCode ? 'Hide Code' : 'Show Code'}
      </button>

      {showCode && (
        <CodeViewer
          code={codeExample}
          title="Modals & Overlays - React/Framer Motion"
        />
      )}
    </div>
  );
};

export default ModalsOverlays;
