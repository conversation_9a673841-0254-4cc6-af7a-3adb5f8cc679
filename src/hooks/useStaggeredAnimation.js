import { useMemo } from 'react';

export const useStaggeredAnimation = (itemCount, delay = 0.1) => {
  const containerVariants = useMemo(
    () => ({
      hidden: { opacity: 0 },
      visible: {
        opacity: 1,
        transition: {
          staggerChildren: delay,
          delayChildren: delay * 0.5,
        },
      },
    }),
    [delay]
  );

  const itemVariants = useMemo(
    () => ({
      hidden: {
        opacity: 0,
        y: 20,
        scale: 0.8,
      },
      visible: {
        opacity: 1,
        y: 0,
        scale: 1,
        transition: {
          type: 'spring',
          stiffness: 300,
          damping: 24,
        },
      },
    }),
    []
  );

  return { containerVariants, itemVariants };
};
