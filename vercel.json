{"name": "framer-motion-examples", "buildCommand": "npm run build", "outputDirectory": "dist", "framework": "vite", "installCommand": "npm install", "devCommand": "npm run dev", "public": true, "regions": ["gru1"], "functions": {"app/**": {"runtime": "nodejs18.x"}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}]}]}