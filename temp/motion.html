<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      Blueprintlabs - Mastering Advanced CSS Animations with Framer Motion
    </title>
    <meta
      name="description"
      content="Interactive demonstrations of advanced CSS animations with Framer Motion - Blueprint Blog" />

    <!-- React and Framer Motion -->
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/framer-motion@10.16.4/dist/framer-motion.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <style>
      /* Blueprint Style Guide Implementation */
      @import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');

      /* CSS Variables - Blueprint Theme System */
      :root {
        /* Dark Theme Colors (Default) */
        --color-dark-bg: #0a0a0a;
        --color-dark-card: #111111;
        --color-dark-border: #222222;
        --color-dark-text: #ffffff;
        --color-dark-text-secondary: #a1a1aa;

        /* Light Theme Colors */
        --color-light-bg: #f1f3f4;
        --color-light-card: #fefefe;
        --color-light-border: #e5e7eb;
        --color-light-text: #1f2937;
        --color-light-text-secondary: #6b7280;

        /* Neon/Cyberpunk Colors - Dark Mode */
        --color-neon-green: #00ff88;
        --color-neon-blue: #00d4ff;
        --color-cyber-purple: #8b5cf6;

        /* Light Mode Accent Colors */
        --color-light-accent-blue: #2563eb;
        --color-light-accent-green: #059669;
        --color-light-accent-purple: #7c3aed;

        /* Adaptive Colors (Change based on theme) */
        --color-bg: var(--color-dark-bg);
        --color-card: var(--color-dark-card);
        --color-border: var(--color-dark-border);
        --color-text: var(--color-dark-text);
        --color-text-secondary: var(--color-dark-text-secondary);
        --color-accent-blue: var(--color-neon-blue);
        --color-accent-green: var(--color-neon-green);
        --color-accent-purple: var(--color-cyber-purple);
      }

      /* Light Theme Override */
      .light {
        --color-bg: var(--color-light-bg);
        --color-card: var(--color-light-card);
        --color-border: var(--color-light-border);
        --color-text: var(--color-light-text);
        --color-text-secondary: var(--color-light-text-secondary);
        --color-accent-blue: var(--color-light-accent-blue);
        --color-accent-green: var(--color-light-accent-green);
        --color-accent-purple: var(--color-light-accent-purple);
      }

      /* Base Reset */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      /* Body with Blueprint styling */
      body {
        font-family: 'Space Grotesk', 'Inter', system-ui, sans-serif;
        background-color: var(--color-bg);
        color: var(--color-text);
        line-height: 1.6;
        overflow-x: hidden;
        min-height: 100vh;
        transition: background-color 0.3s ease, color 0.3s ease;
      }

      /* Dark mode background pattern */
      .dark body {
        background-image: radial-gradient(
          circle at 1px 1px,
          rgba(0, 245, 255, 0.15) 1px,
          transparent 0
        );
        background-size: 20px 20px;
      }

      /* Light mode background pattern */
      .light body {
        background-image: radial-gradient(
          circle at 1px 1px,
          rgba(37, 99, 235, 0.08) 1px,
          transparent 0
        );
        background-size: 20px 20px;
      }

      /* Navbar - Blueprint Style */
      .navbar {
        position: fixed;
        top: 0;
        z-index: 50;
        width: 100%;
        background-color: rgba(17, 17, 17, 0.9);
        backdrop-filter: blur(12px);
        border-bottom: 1px solid var(--color-border);
        transition: all 0.3s ease;
      }

      .light .navbar {
        background-color: rgba(254, 254, 254, 0.9);
      }

      .navbar-container {
        max-width: 1280px;
        margin: 0 auto;
        padding: 0 1rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 4rem;
      }

      .navbar-logo {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--color-accent-blue);
        text-decoration: none;
        transition: all 0.3s ease;
      }

      .navbar-logo:hover {
        transform: scale(1.05);
      }

      .dark .navbar-logo {
        text-shadow: 0 0 10px currentColor;
      }

      .theme-toggle {
        background: transparent;
        border: 1px solid var(--color-border);
        color: var(--color-text);
        padding: 0.5rem;
        border-radius: 0.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .theme-toggle:hover {
        background-color: rgba(0, 212, 255, 0.1);
        border-color: var(--color-accent-blue);
        transform: translateY(-1px);
      }

      .dark .theme-toggle:hover {
        box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
      }

      /* Main Content */
      .main-content {
        flex: 1;
        padding-top: 4rem;
      }

      /* Container */
      .container {
        max-width: 1152px;
        margin: 0 auto;
        padding: 1rem;
      }

      /* Hero Section - Blueprint Style */
      .hero {
        height: 15vh;
        min-height: 200px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        margin-bottom: 3rem;
        position: relative;
      }

      .hero h1 {
        font-size: clamp(2rem, 5vw, 4rem);
        font-weight: 700;
        margin-bottom: 1rem;
        background: linear-gradient(
          135deg,
          var(--color-accent-blue),
          var(--color-accent-purple)
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .dark .hero h1 {
        text-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
      }

      .hero p {
        font-family: 'JetBrains Mono', monospace;
        font-size: clamp(0.875rem, 2vw, 1rem);
        color: var(--color-text-secondary);
        max-width: 600px;
      }

      /* Cards - Blueprint Style */
      .card {
        background-color: var(--color-card);
        border: 1px solid var(--color-border);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
        position: relative;
      }

      .card:hover {
        transform: translateY(-2px);
      }

      .dark .card:hover {
        box-shadow: 0 10px 30px rgba(0, 212, 255, 0.1);
        border-color: rgba(0, 212, 255, 0.3);
      }

      .light .card:hover {
        box-shadow: 0 10px 30px rgba(37, 99, 235, 0.1);
      }

      /* Section Headers */
      .section-header {
        color: var(--color-text);
        font-size: 1.875rem;
        font-weight: 600;
        margin-bottom: 1rem;
        position: relative;
      }

      .section-header::after {
        content: '';
        position: absolute;
        bottom: -0.5rem;
        left: 0;
        width: 4rem;
        height: 3px;
        background: var(--color-accent-blue);
        border-radius: 2px;
      }

      .dark .section-header::after {
        box-shadow: 0 0 10px var(--color-accent-blue);
      }

      /* Demo Grid */
      .demo-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 1.5rem;
        margin-top: 2rem;
      }

      /* Demo Box */
      .demo-box {
        background-color: var(--color-card);
        border: 1px solid var(--color-border);
        border-radius: 1rem;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }

      .demo-box:hover {
        transform: translateY(-4px);
        border-color: var(--color-accent-blue);
      }

      .dark .demo-box:hover {
        box-shadow: 0 15px 40px rgba(0, 212, 255, 0.15);
        background: linear-gradient(
          135deg,
          rgba(0, 212, 255, 0.05) 0%,
          rgba(139, 92, 246, 0.05) 100%
        );
      }

      /* Animated Elements */
      .animated-box {
        width: 80px;
        height: 80px;
        background: linear-gradient(
          45deg,
          var(--color-accent-blue),
          var(--color-accent-purple)
        );
        border-radius: 1rem;
        margin: 1.5rem auto;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 700;
        font-size: 1.5rem;
      }

      .dark .animated-box {
        box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
      }

      /* Buttons - Blueprint Style */
      .btn-primary {
        background-color: var(--color-accent-blue);
        color: var(--color-bg);
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        min-height: 2.75rem;
        display: inline-flex;
        align-items: center;
        justify-content: center;
      }

      .btn-primary:hover {
        opacity: 0.9;
        transform: translateY(-1px);
      }

      .dark .btn-primary:hover {
        box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);
      }

      .btn-secondary {
        background: transparent;
        color: var(--color-accent-blue);
        border: 1px solid var(--color-accent-blue);
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        min-height: 2.75rem;
      }

      .btn-secondary:hover {
        background-color: var(--color-accent-blue);
        color: var(--color-bg);
      }

      /* List Items */
      .list-item {
        background-color: var(--color-card);
        border: 1px solid var(--color-border);
        padding: 1rem 1.5rem;
        margin: 0.75rem 0;
        border-radius: 0.75rem;
        font-weight: 500;
        color: var(--color-text);
        transition: all 0.3s ease;
      }

      .list-item:hover {
        border-color: var(--color-accent-blue);
      }

      /* Progress Components */
      .progress-container {
        background-color: var(--color-border);
        border-radius: 50px;
        height: 1.5rem;
        margin: 1.5rem 0;
        overflow: hidden;
      }

      .progress-bar {
        height: 100%;
        background: linear-gradient(
          90deg,
          var(--color-accent-blue),
          var(--color-accent-purple)
        );
        border-radius: 50px;
        transition: all 0.3s ease;
      }

      .dark .progress-bar {
        box-shadow: 0 0 15px rgba(0, 212, 255, 0.5);
      }

      /* Modal Styles */
      .modal-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        backdrop-filter: blur(8px);
        z-index: 1000;
      }

      .modal {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: var(--color-card);
        border: 1px solid var(--color-border);
        border-radius: 1rem;
        padding: 2rem;
        z-index: 1001;
        max-width: 450px;
        width: 90%;
      }

      .dark .modal {
        box-shadow: 0 25px 50px rgba(0, 212, 255, 0.2);
      }

      /* Chart Components */
      .chart-container {
        display: flex;
        align-items: end;
        justify-content: space-around;
        height: 200px;
        background-color: var(--color-card);
        border: 1px solid var(--color-border);
        border-radius: 1rem;
        padding: 1.5rem;
      }

      .bar {
        width: 40px;
        background: linear-gradient(
          to top,
          var(--color-accent-blue),
          var(--color-accent-purple)
        );
        border-radius: 0.5rem 0.5rem 0 0;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .bar:hover {
        transform: scale(1.05);
      }

      .dark .bar {
        box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
      }

      /* Skeleton Loading */
      .skeleton-card {
        background-color: var(--color-card);
        border: 1px solid var(--color-border);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin: 1rem;
      }

      .skeleton-line {
        height: 1.5rem;
        background-color: var(--color-border);
        border-radius: 0.375rem;
        margin: 0.75rem 0;
      }

      .skeleton-line.short {
        width: 65%;
      }

      /* Expandable Card */
      .expandable-card {
        background: linear-gradient(
          135deg,
          rgba(0, 255, 136, 0.1) 0%,
          rgba(0, 212, 255, 0.1) 100%
        );
        border: 1px solid rgba(0, 212, 255, 0.2);
        border-radius: 1rem;
        padding: 1.5rem;
        margin: 1rem;
        cursor: pointer;
        overflow: hidden;
        transition: all 0.3s ease;
      }

      .expandable-card:hover {
        border-color: var(--color-accent-blue);
        transform: translateY(-2px);
      }

      /* Drag Card */
      .drag-card {
        background-color: var(--color-card);
        border: 1px solid var(--color-border);
        border-radius: 1rem;
        padding: 1.5rem;
        margin: 1rem;
        cursor: grab;
        user-select: none;
        transition: all 0.3s ease;
      }

      .drag-card:active {
        cursor: grabbing;
      }

      .drag-card:hover {
        border-color: var(--color-accent-blue);
      }

      /* Code Snippet */
      .code-snippet {
        background-color: #1a1a1a;
        color: #e2e8f0;
        padding: 1.5rem;
        border-radius: 0.75rem;
        margin: 1.5rem 0;
        font-family: 'JetBrains Mono', monospace;
        font-size: 0.875rem;
        overflow-x: auto;
        border: 1px solid #333;
      }

      .light .code-snippet {
        background-color: #f8f9fa;
        color: #2d3748;
        border-color: #e2e8f0;
      }

      /* Final Section */
      .final-section {
        text-align: center;
        background: linear-gradient(
          135deg,
          var(--color-accent-blue),
          var(--color-accent-purple)
        );
        color: white;
        border-radius: 1rem;
        margin-top: 3rem;
      }

      .final-section h2 {
        color: white;
      }

      .final-section h2::after {
        background: rgba(255, 255, 255, 0.8);
      }

      .final-section p {
        color: rgba(255, 255, 255, 0.95);
      }

      /* Demo Description */
      .demo-description {
        font-size: 0.875rem;
        color: var(--color-text-secondary);
        margin-top: 1rem;
        font-weight: 500;
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .navbar-container {
          padding: 0 1rem;
        }

        .hero h1 {
          font-size: 2rem;
        }

        .demo-grid {
          grid-template-columns: 1fr;
        }

        .container {
          padding: 1rem;
        }

        .card {
          padding: 1.25rem;
        }

        .demo-box {
          padding: 1.5rem;
        }
      }

      /* Animation Keyframes */
      @keyframes glow {
        from {
          box-shadow: 0 0 10px currentColor;
        }
        to {
          box-shadow: 0 0 20px currentColor, 0 0 30px currentColor;
        }
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
        }
        to {
          opacity: 1;
        }
      }

      .animate-glow {
        animation: glow 2s ease-in-out infinite alternate;
      }

      .animate-fade-in {
        animation: fadeIn 0.5s ease-out;
      }

      /* Utility Classes */
      .text-gradient {
        background: linear-gradient(
          135deg,
          var(--color-accent-blue),
          var(--color-accent-purple)
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .glow-text {
        text-shadow: 0 0 10px currentColor;
      }
    </style>
  </head>
  <body class="dark antialiased">
    <!-- Navbar - Blueprint Style -->
    <nav class="navbar">
      <div class="navbar-container">
        <a href="https://blueprintblog.tech" class="navbar-logo">
          <i data-lucide="zap" style="width: 24px; height: 24px"></i>
          Blueprint
        </a>
        <button class="theme-toggle" onclick="toggleTheme()">
          <i data-lucide="sun" style="width: 20px; height: 20px"></i>
        </button>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
      <div id="root"></div>
    </main>

    <script type="text/babel">
      const { useState, useEffect, useMemo, useCallback } = React;
      const {
        motion,
        AnimatePresence,
        useAnimation,
        useMotionValue,
        useTransform,
      } = Motion;

      // Theme Management
      function toggleTheme() {
        document.body.classList.toggle('dark');
        document.body.classList.toggle('light');
      }

      // Basic Animation Component
      function BasicAnimation() {
        return (
          <motion.div
            className="animated-box"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}>
            ✨
          </motion.div>
        );
      }

      // Staggered List Animation
      function StaggeredList() {
        const [isVisible, setIsVisible] = useState(false);

        const containerVariants = {
          hidden: { opacity: 0 },
          visible: {
            opacity: 1,
            transition: {
              delayChildren: 0.3,
              staggerChildren: 0.2,
            },
          },
        };

        const itemVariants = {
          hidden: { y: 20, opacity: 0 },
          visible: {
            y: 0,
            opacity: 1,
            transition: { type: 'spring', stiffness: 100 },
          },
        };

        return (
          <div>
            <button
              className="btn-primary"
              onClick={() => setIsVisible(!isVisible)}>
              {isVisible ? 'Hide' : 'Show'} List
            </button>
            <AnimatePresence>
              {isVisible && (
                <motion.ul
                  variants={containerVariants}
                  initial="hidden"
                  animate="visible"
                  exit="hidden"
                  style={{ listStyle: 'none', padding: 0, marginTop: '1rem' }}>
                  {[
                    'First Item',
                    'Second Item',
                    'Third Item',
                    'Fourth Item',
                  ].map((item, index) => (
                    <motion.li
                      key={index}
                      variants={itemVariants}
                      className="list-item">
                      {item}
                    </motion.li>
                  ))}
                </motion.ul>
              )}
            </AnimatePresence>
          </div>
        );
      }

      // Spring Animation
      function SpringAnimation() {
        const [clicked, setClicked] = useState(false);

        return (
          <motion.div
            className="animated-box"
            animate={{
              scale: clicked ? 1.2 : 1,
              rotate: clicked ? 180 : 0,
            }}
            transition={{
              type: 'spring',
              stiffness: 260,
              damping: 20,
              mass: 1,
            }}
            onClick={() => setClicked(!clicked)}
            style={{ cursor: 'pointer' }}>
            🎯
          </motion.div>
        );
      }

      // Advanced Drag with Swipe Detection
      function DraggableCard() {
        const [dragCount, setDragCount] = useState(0);

        return (
          <motion.div
            className="drag-card"
            drag
            dragConstraints={{ left: -100, right: 100, top: -100, bottom: 100 }}
            dragElastic={0.1}
            whileDrag={{ scale: 1.05, rotate: 5 }}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onDragEnd={(event, info) => {
              if (Math.abs(info.offset.x) > 50) {
                setDragCount((prev) => prev + 1);
              }
            }}>
            <h3 style={{ marginBottom: '0.5rem', color: 'var(--color-text)' }}>
              Advanced Drag! 🎮
            </h3>
            <p
              style={{
                color: 'var(--color-text-secondary)',
                fontSize: '0.875rem',
              }}>
              Swipes detected: {dragCount}
            </p>
            <small style={{ color: 'var(--color-text-secondary)' }}>
              Drag beyond 50px to trigger swipe
            </small>
          </motion.div>
        );
      }

      // Performance Optimized Component with useMemo
      function OptimizedAnimation({ isActive }) {
        const variants = useMemo(
          () => ({
            inactive: {
              opacity: 0.5,
              scale: 0.95,
              backgroundColor: 'var(--color-border)',
            },
            active: {
              opacity: 1,
              scale: 1,
              backgroundColor: 'var(--color-accent-blue)',
              transition: { duration: 0.3 },
            },
          }),
          []
        );

        return (
          <motion.div
            className="animated-box"
            variants={variants}
            animate={isActive ? 'active' : 'inactive'}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontWeight: 'bold',
            }}>
            {isActive ? '🚀' : '💤'}
          </motion.div>
        );
      }

      // Performance Demo Container
      function PerformanceDemo() {
        const [isActive, setIsActive] = useState(false);

        return (
          <div>
            <button
              className="btn-secondary"
              onClick={() => setIsActive(!isActive)}>
              {isActive ? 'Deactivate' : 'Activate'}
            </button>
            <div style={{ marginTop: '1rem' }}>
              <OptimizedAnimation isActive={isActive} />
            </div>
            <p className="demo-description">Optimized with useMemo variants</p>
          </div>
        );
      }

      // MotionValues and Scroll Indicator
      function ScrollIndicator() {
        const scrollY = useMotionValue(0);
        const [scrollProgress, setScrollProgress] = useState(0);

        useEffect(() => {
          const updateScrollY = () => {
            const progress =
              window.scrollY /
              (document.body.scrollHeight - window.innerHeight);
            const clampedProgress = Math.min(Math.max(progress * 100, 0), 100);
            scrollY.set(clampedProgress);
            setScrollProgress(clampedProgress);
          };

          window.addEventListener('scroll', updateScrollY);
          return () => window.removeEventListener('scroll', updateScrollY);
        }, [scrollY]);

        return (
          <div>
            <div className="progress-container">
              <motion.div
                className="progress-bar"
                style={{
                  width: useTransform(scrollY, [0, 100], ['0%', '100%']),
                }}
              />
            </div>
            <p className="demo-description">
              Scroll progress: {Math.round(scrollProgress)}%
            </p>
            <small style={{ color: 'var(--color-text-secondary)' }}>
              Uses MotionValues for performance
            </small>
          </div>
        );
      }

      // Custom Animation Hook
      function useStaggeredAnimation(count, delay = 0.1) {
        const controls = useAnimation();

        const staggerAnimation = useCallback(async () => {
          await controls.start((i) => ({
            opacity: 1,
            y: 0,
            scale: 1,
            transition: { delay: i * delay, type: 'spring', stiffness: 100 },
          }));
        }, [controls, delay]);

        const resetAnimation = useCallback(async () => {
          await controls.start((i) => ({
            opacity: 0,
            y: 20,
            scale: 0.8,
            transition: { delay: i * 0.05 },
          }));
        }, [controls]);

        return { controls, staggerAnimation, resetAnimation };
      }

      // Custom Hook Demo
      function CustomHookDemo() {
        const { controls, staggerAnimation, resetAnimation } =
          useStaggeredAnimation(4, 0.15);

        return (
          <div>
            <div
              style={{ display: 'flex', gap: '0.5rem', marginBottom: '1rem' }}>
              <button className="btn-primary" onClick={staggerAnimation}>
                Animate
              </button>
              <button className="btn-secondary" onClick={resetAnimation}>
                Reset
              </button>
            </div>
            <div style={{ display: 'flex', gap: '0.5rem', flexWrap: 'wrap' }}>
              {[0, 1, 2, 3].map((i) => (
                <motion.div
                  key={i}
                  custom={i}
                  animate={controls}
                  initial={{ opacity: 0, y: 20, scale: 0.8 }}
                  style={{
                    width: '40px',
                    height: '40px',
                    backgroundColor: 'var(--color-accent-purple)',
                    borderRadius: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontWeight: 'bold',
                  }}>
                  {i + 1}
                </motion.div>
              ))}
            </div>
            <p className="demo-description">
              Custom useStaggeredAnimation hook
            </p>
          </div>
        );
      }

      // Page Transition Simulator
      function PageTransitionDemo() {
        const [currentPage, setCurrentPage] = useState(0);
        const pages = ['Home', 'About', 'Services', 'Contact'];

        const pageVariants = {
          initial: { opacity: 0, x: '-100vw', scale: 0.8 },
          in: { opacity: 1, x: 0, scale: 1 },
          out: { opacity: 0, x: '100vw', scale: 1.2 },
        };

        const pageTransition = {
          type: 'tween',
          ease: 'anticipate',
          duration: 0.5,
        };

        return (
          <div>
            <div
              style={{
                display: 'flex',
                gap: '0.5rem',
                marginBottom: '1rem',
                flexWrap: 'wrap',
              }}>
              {pages.map((page, index) => (
                <button
                  key={page}
                  className={
                    currentPage === index ? 'btn-primary' : 'btn-secondary'
                  }
                  onClick={() => setCurrentPage(index)}>
                  {page}
                </button>
              ))}
            </div>
            <div
              style={{
                height: '120px',
                backgroundColor: 'var(--color-card)',
                border: '1px solid var(--color-border)',
                borderRadius: '0.5rem',
                overflow: 'hidden',
                position: 'relative',
              }}>
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentPage}
                  initial="initial"
                  animate="in"
                  exit="out"
                  variants={pageVariants}
                  transition={pageTransition}
                  style={{
                    position: 'absolute',
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    background: `linear-gradient(135deg, var(--color-accent-blue), var(--color-accent-purple))`,
                    color: 'white',
                    fontSize: '1.5rem',
                    fontWeight: 'bold',
                  }}>
                  {pages[currentPage]} Page
                </motion.div>
              </AnimatePresence>
            </div>
            <p className="demo-description">
              Advanced page transitions with AnimatePresence
            </p>
          </div>
        );
      }

      // Accessibility-Aware Animation
      function AccessibleAnimation() {
        const [shouldReduceMotion, setShouldReduceMotion] = useState(false);
        const [isAnimating, setIsAnimating] = useState(false);

        // Simulate prefers-reduced-motion detection
        useEffect(() => {
          const mediaQuery = window.matchMedia(
            '(prefers-reduced-motion: reduce)'
          );
          setShouldReduceMotion(mediaQuery.matches);

          const handler = (e) => setShouldReduceMotion(e.matches);
          mediaQuery.addEventListener('change', handler);
          return () => mediaQuery.removeEventListener('change', handler);
        }, []);

        const triggerAnimation = () => {
          setIsAnimating(true);
          setTimeout(() => setIsAnimating(false), 1000);
        };

        return (
          <div>
            <div style={{ marginBottom: '1rem' }}>
              <button className="btn-primary" onClick={triggerAnimation}>
                Trigger Animation
              </button>
              <label
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  marginTop: '0.5rem',
                  color: 'var(--color-text-secondary)',
                  fontSize: '0.875rem',
                }}>
                <input
                  type="checkbox"
                  checked={shouldReduceMotion}
                  onChange={(e) => setShouldReduceMotion(e.target.checked)}
                />
                Reduce motion (simulate accessibility preference)
              </label>
            </div>
            <motion.div
              className="animated-box"
              animate={
                isAnimating
                  ? {
                      x: shouldReduceMotion ? 0 : 100,
                      rotate: shouldReduceMotion ? 0 : 360,
                      scale: shouldReduceMotion ? 1 : 1.2,
                    }
                  : {
                      x: 0,
                      rotate: 0,
                      scale: 1,
                    }
              }
              transition={{
                duration: shouldReduceMotion ? 0.1 : 0.8,
                type: shouldReduceMotion ? 'tween' : 'spring',
              }}>
              ♿
            </motion.div>
            <p className="demo-description">
              Respects reduced motion preferences
            </p>
          </div>
        );
      }

      // Expandable Card
      function ExpandableCard() {
        const [isExpanded, setIsExpanded] = useState(false);

        return (
          <motion.div
            layout
            className="expandable-card"
            onClick={() => setIsExpanded(!isExpanded)}>
            <motion.h3
              layout="position"
              style={{ color: 'var(--color-text)', marginBottom: '1rem' }}>
              Layout Animations 📦
            </motion.h3>
            {isExpanded && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}>
                <p
                  style={{
                    color: 'var(--color-text-secondary)',
                    marginBottom: '1rem',
                  }}>
                  This content appears when the card is expanded. Layout
                  animations automatically calculate and animate changes in size
                  and position.
                </p>
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: 'spring' }}
                  style={{
                    padding: '0.5rem',
                    backgroundColor: 'rgba(0, 212, 255, 0.1)',
                    borderRadius: '0.5rem',
                    border: '1px solid rgba(0, 212, 255, 0.3)',
                  }}>
                  <small style={{ color: 'var(--color-text-secondary)' }}>
                    Nested animation with delay
                  </small>
                </motion.div>
              </motion.div>
            )}
          </motion.div>
        );
      }

      // Loading Skeleton
      function LoadingSkeleton() {
        return (
          <motion.div
            className="skeleton-card"
            animate={{
              backgroundColor: [
                'var(--color-border)',
                'rgba(0, 212, 255, 0.1)',
                'var(--color-border)',
              ],
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: 'easeInOut',
            }}>
            <div className="skeleton-line" />
            <div className="skeleton-line short" />
            <div className="skeleton-line" />
          </motion.div>
        );
      }

      // Progress Bar
      function ProgressBar() {
        const [progress, setProgress] = useState(0);

        const startProgress = () => {
          setProgress(0);
          const interval = setInterval(() => {
            setProgress((prev) => {
              if (prev >= 100) {
                clearInterval(interval);
                return 100;
              }
              return prev + 2;
            });
          }, 50);
        };

        return (
          <div>
            <button className="btn-secondary" onClick={startProgress}>
              Start Progress
            </button>
            <div className="progress-container">
              <motion.div
                className="progress-bar"
                initial={{ width: '0%' }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.1 }}
              />
            </div>
            <p className="demo-description">{progress}% complete</p>
          </div>
        );
      }

      // Animated Chart
      function AnimatedChart() {
        const [animate, setAnimate] = useState(false);
        const data = [
          { id: 1, value: 65, label: 'A' },
          { id: 2, value: 80, label: 'B' },
          { id: 3, value: 45, label: 'C' },
          { id: 4, value: 90, label: 'D' },
          { id: 5, value: 70, label: 'E' },
        ];

        return (
          <div>
            <button
              className="btn-secondary"
              onClick={() => setAnimate(!animate)}
              style={{ marginBottom: '1rem' }}>
              {animate ? 'Reset' : 'Animate'} Chart
            </button>
            <div className="chart-container">
              {data.map((item, index) => (
                <motion.div
                  key={item.id}
                  className="bar"
                  initial={{ height: 0 }}
                  animate={{ height: animate ? `${item.value}%` : 0 }}
                  transition={{
                    delay: index * 0.1,
                    type: 'spring',
                    stiffness: 100,
                  }}
                  whileHover={{ scale: 1.05 }}
                  style={{
                    display: 'flex',
                    alignItems: 'end',
                    justifyContent: 'center',
                    color: 'white',
                    fontWeight: 'bold',
                    fontSize: '0.75rem',
                    paddingBottom: '0.25rem',
                  }}>
                  {item.label}
                </motion.div>
              ))}
            </div>
          </div>
        );
      }

      // Animated Modal
      function AnimatedModal() {
        const [isOpen, setIsOpen] = useState(false);

        return (
          <div>
            <button className="btn-primary" onClick={() => setIsOpen(true)}>
              Open Modal
            </button>
            <AnimatePresence>
              {isOpen && (
                <>
                  <motion.div
                    className="modal-backdrop"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    onClick={() => setIsOpen(false)}
                  />
                  <motion.div
                    className="modal"
                    initial={{ opacity: 0, scale: 0.8, y: 50 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    exit={{ opacity: 0, scale: 0.8, y: 50 }}
                    transition={{
                      type: 'spring',
                      damping: 25,
                      stiffness: 500,
                    }}>
                    <h3
                      style={{
                        color: 'var(--color-text)',
                        marginBottom: '1rem',
                        fontSize: '1.5rem',
                      }}>
                      Advanced Modal 🎭
                    </h3>
                    <p
                      style={{
                        color: 'var(--color-text-secondary)',
                        marginBottom: '1.5rem',
                      }}>
                      This modal uses smooth enter and exit animations with
                      spring physics. Click the backdrop or close button to
                      dismiss.
                    </p>
                    <button
                      className="btn-primary"
                      onClick={() => setIsOpen(false)}>
                      Close Modal
                    </button>
                  </motion.div>
                </>
              )}
            </AnimatePresence>
          </div>
        );
      }

      // Main App Component
      function App() {
        return (
          <div className="container">
            {/* Hero Section */}
            <motion.div
              className="hero"
              initial={{ opacity: 0, y: -50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}>
              <h1>Framer Motion</h1>
              <p>
                Interactive demonstrations of advanced CSS animation concepts
                with Framer Motion for React
              </p>
            </motion.div>

            {/* Demo Sections */}
            <motion.div
              className="card"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}>
              <h2 className="section-header">1. Basic Animations</h2>
              <p
                style={{
                  color: 'var(--color-text-secondary)',
                  marginBottom: '1.5rem',
                }}>
                Fundamental concepts: initial state, animate target, and
                transition configuration.
              </p>
              <div className="demo-grid">
                <div className="demo-box">
                  <BasicAnimation />
                  <p className="demo-description">Simple entrance animation</p>
                </div>
              </div>
            </motion.div>

            <motion.div
              className="card"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4 }}>
              <h2 className="section-header">2. Complex Sequences</h2>
              <p
                style={{
                  color: 'var(--color-text-secondary)',
                  marginBottom: '1.5rem',
                }}>
                Orchestrating multiple elements with staggered timing and child
                animations.
              </p>
              <div className="demo-grid">
                <div className="demo-box">
                  <StaggeredList />
                </div>
              </div>
            </motion.div>

            <motion.div
              className="card"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.6 }}>
              <h2 className="section-header">3. Physics-Based Animations</h2>
              <p
                style={{
                  color: 'var(--color-text-secondary)',
                  marginBottom: '1.5rem',
                }}>
                Using spring system with mass, stiffness, and damping for
                natural, organic movement.
              </p>
              <div className="demo-grid">
                <div className="demo-box">
                  <SpringAnimation />
                  <p className="demo-description">
                    Click to interact with spring physics
                  </p>
                </div>
              </div>
            </motion.div>

            <motion.div
              className="card"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.8 }}>
              <h2 className="section-header">
                4. Advanced Gesture Interactions
              </h2>
              <p
                style={{
                  color: 'var(--color-text-secondary)',
                  marginBottom: '1.5rem',
                }}>
                Rich gesture support with drag constraints, swipe detection, and
                visual feedback.
              </p>
              <div className="demo-grid">
                <div className="demo-box">
                  <DraggableCard />
                </div>
              </div>
            </motion.div>

            <motion.div
              className="card"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 1.0 }}>
              <h2 className="section-header">5. Layout Animations</h2>
              <p
                style={{
                  color: 'var(--color-text-secondary)',
                  marginBottom: '1.5rem',
                }}>
                Automatic transitions between different layout states with
                nested animations.
              </p>
              <div className="demo-grid">
                <div className="demo-box">
                  <ExpandableCard />
                </div>
              </div>
            </motion.div>

            <motion.div
              className="card"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 1.2 }}>
              <h2 className="section-header">6. Performance Optimization</h2>
              <p
                style={{
                  color: 'var(--color-text-secondary)',
                  marginBottom: '1.5rem',
                }}>
                Optimized animations using useMemo for variants and efficient
                state management.
              </p>
              <div className="demo-grid">
                <div className="demo-box">
                  <PerformanceDemo />
                </div>
              </div>
            </motion.div>

            <motion.div
              className="card"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 1.4 }}>
              <h2 className="section-header">7. MotionValues & Scroll</h2>
              <p
                style={{
                  color: 'var(--color-text-secondary)',
                  marginBottom: '1.5rem',
                }}>
                High-performance scroll-driven animations using MotionValues
                that bypass React renders.
              </p>
              <div className="demo-grid">
                <div className="demo-box">
                  <ScrollIndicator />
                </div>
              </div>
            </motion.div>

            <motion.div
              className="card"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 1.6 }}>
              <h2 className="section-header">8. Custom Animation Hooks</h2>
              <p
                style={{
                  color: 'var(--color-text-secondary)',
                  marginBottom: '1.5rem',
                }}>
                Reusable animation logic with custom hooks for staggered
                animations and controls.
              </p>
              <div className="demo-grid">
                <div className="demo-box">
                  <CustomHookDemo />
                </div>
              </div>
            </motion.div>

            <motion.div
              className="card"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 1.8 }}>
              <h2 className="section-header">9. Page Transitions</h2>
              <p
                style={{
                  color: 'var(--color-text-secondary)',
                  marginBottom: '1.5rem',
                }}>
                Advanced page transitions with AnimatePresence and custom easing
                functions.
              </p>
              <div className="demo-grid">
                <div className="demo-box">
                  <PageTransitionDemo />
                </div>
              </div>
            </motion.div>

            <motion.div
              className="card"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 2.0 }}>
              <h2 className="section-header">
                10. Accessibility Considerations
              </h2>
              <p
                style={{
                  color: 'var(--color-text-secondary)',
                  marginBottom: '1.5rem',
                }}>
                Respecting user preferences for reduced motion and providing
                accessible alternatives.
              </p>
              <div className="demo-grid">
                <div className="demo-box">
                  <AccessibleAnimation />
                </div>
              </div>
            </motion.div>

            <motion.div
              className="card"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 2.2 }}>
              <h2 className="section-header">11. Loading States</h2>
              <p
                style={{
                  color: 'var(--color-text-secondary)',
                  marginBottom: '1.5rem',
                }}>
                Skeleton screens and progress indicators with smooth, continuous
                animations.
              </p>
              <div className="demo-grid">
                <div className="demo-box">
                  <LoadingSkeleton />
                  <p className="demo-description">Skeleton loading animation</p>
                </div>
                <div className="demo-box">
                  <ProgressBar />
                </div>
              </div>
            </motion.div>

            <motion.div
              className="card"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 2.4 }}>
              <h2 className="section-header">12. Data Visualization</h2>
              <p
                style={{
                  color: 'var(--color-text-secondary)',
                  marginBottom: '1.5rem',
                }}>
                Interactive charts with staggered animations, hover
                interactions, and manual controls.
              </p>
              <div className="demo-grid">
                <div className="demo-box">
                  <AnimatedChart />
                </div>
              </div>
            </motion.div>

            <motion.div
              className="card"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 2.6 }}>
              <h2 className="section-header">13. Modals & Overlays</h2>
              <p
                style={{
                  color: 'var(--color-text-secondary)',
                  marginBottom: '1.5rem',
                }}>
                Smooth transitions for elements that enter and exit the DOM
                using AnimatePresence.
              </p>
              <div className="demo-grid">
                <div className="demo-box">
                  <AnimatedModal />
                </div>
              </div>
            </motion.div>

            {/* Final Section */}
            <motion.div
              className="card final-section"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 2.8 }}>
              <h2 className="section-header">✨ Ready to Get Started?</h2>
              <p style={{ fontSize: '1.2rem', marginBottom: '2rem' }}>
                Unleash the power of Framer Motion in your React projects!
              </p>
              <div className="code-snippet">npm install framer-motion</div>
              <p style={{ fontSize: '1.1rem', marginTop: '1.5rem' }}>
                Read the complete Medium article to learn all the details,
                advanced techniques, and best practices for creating delightful
                user experiences.
              </p>
            </motion.div>
          </div>
        );
      }

      // Initialize Lucide icons
      lucide.createIcons();

      // Render the app
      ReactDOM.render(<App />, document.getElementById('root'));
    </script>
  </body>
</html>
